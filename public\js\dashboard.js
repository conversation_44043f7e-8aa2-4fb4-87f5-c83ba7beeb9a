// Dashboard JavaScript Functions

function showLoading() {
    $('#loadingModal').modal('show');
}

function hideLoading() {
    $('#loadingModal').modal('hide');
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

function refreshDashboard() {
    showLoading();
    
    fetch('/dashboard/', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboardData(data.data);
            showAlert('success', 'Dashboard refreshed successfully');
        } else {
            showAlert('error', 'Failed to refresh dashboard');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while refreshing');
    })
    .finally(() => {
        hideLoading();
    });
}

function updateDashboardData(data) {
    // Update key metrics
    document.getElementById('total-students').textContent = data.key_metrics.total_students;
    document.getElementById('attendance-rate').textContent = data.key_metrics.attendance_rate + '%';
    document.getElementById('present-today').textContent = data.key_metrics.today_present;
    document.getElementById('absent-today').textContent = data.key_metrics.today_absent;
    
    // Update charts
    updateWeeklyTrendsChart(data.weekly_trends);
    updateGradeDistributionChart(data.chart_data.grade_distribution);
    
    // Update at-risk students table
    updateAtRiskTable(data.at_risk_students);
    
    // Update recent activities
    updateRecentActivities(data.recent_activities);
}

function updateWeeklyTrendsChart(weeklyData) {
    if (weeklyTrendsChart) {
        weeklyTrendsChart.data.labels = weeklyData.map(d => d.day);
        weeklyTrendsChart.data.datasets[0].data = weeklyData.map(d => d.attendance_rate);
        weeklyTrendsChart.update();
    }
}

function updateGradeDistributionChart(gradeData) {
    if (gradeDistributionChart) {
        gradeDistributionChart.data.labels = gradeData.map(d => d.grade_level);
        gradeDistributionChart.data.datasets[0].data = gradeData.map(d => d.count);
        gradeDistributionChart.update();
    }
}

function updateAtRiskTable(students) {
    const tbody = document.querySelector('#at-risk-table tbody');
    tbody.innerHTML = '';
    
    students.forEach(student => {
        const riskBadgeClass = student.risk_level === 'high' ? 'danger' : 
                              (student.risk_level === 'medium' ? 'warning' : 'success');
        
        const row = `
            <tr>
                <td>${student.name}</td>
                <td>${student.grade_level}-${student.section}</td>
                <td>${student.attendance_rate}%</td>
                <td>
                    <span class="badge badge-${riskBadgeClass}">
                        ${student.risk_level.charAt(0).toUpperCase() + student.risk_level.slice(1)}
                    </span>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function updateRecentActivities(activities) {
    const container = document.getElementById('recent-activities');
    container.innerHTML = '';
    
    activities.forEach(activity => {
        const statusBadgeClass = activity.status === 'present' ? 'success' : 
                                (activity.status === 'absent' ? 'danger' : 'warning');
        
        const activityHtml = `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${activity.student_name}</h6>
                    <small>${activity.time}</small>
                </div>
                <p class="mb-1">
                    <span class="badge badge-${statusBadgeClass}">
                        ${activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                    </span>
                    in ${activity.subject_name}
                </p>
                <small>Teacher: ${activity.teacher_name}</small>
            </div>
        `;
        container.innerHTML += activityHtml;
    });
}

function loadAttendanceStats() {
    showLoading();
    
    fetch('/dashboard/attendance-stats', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Attendance Stats:', data.data);
            // Handle attendance stats display
        } else {
            showAlert('error', 'Failed to load attendance statistics');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while loading attendance statistics');
    })
    .finally(() => {
        hideLoading();
    });
}

function loadStudentRisk() {
    showLoading();
    
    fetch('/dashboard/student-risk', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Student Risk Assessment:', data.data);
            // Handle student risk display
        } else {
            showAlert('error', 'Failed to load student risk assessment');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while loading student risk assessment');
    })
    .finally(() => {
        hideLoading();
    });
}

function loadTrendAnalysis(period = 'monthly') {
    showLoading();
    
    fetch(`/dashboard/trend-analysis?period=${period}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Trend Analysis:', data.data);
            // Handle trend analysis display
        } else {
            showAlert('error', 'Failed to load trend analysis');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while loading trend analysis');
    })
    .finally(() => {
        hideLoading();
    });
}

function generateSF2Report() {
    // Show form modal for SF2 parameters
    const formHtml = `
        <div class="modal fade" id="sf2Modal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Generate SF2 Report</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <form id="sf2Form">
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Grade Level</label>
                                <input type="text" class="form-control" name="grade_level" required>
                            </div>
                            <div class="form-group">
                                <label>Section</label>
                                <input type="text" class="form-control" name="section" required>
                            </div>
                            <div class="form-group">
                                <label>Subject</label>
                                <select class="form-control" name="subject_id" required>
                                    <option value="">Select Subject</option>
                                    <!-- Add subject options dynamically -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Date From</label>
                                <input type="date" class="form-control" name="date_from" required>
                            </div>
                            <div class="form-group">
                                <label>Date To</label>
                                <input type="date" class="form-control" name="date_to" required>
                            </div>
                            <div class="form-group">
                                <label>Format</label>
                                <select class="form-control" name="format">
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Generate Report</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
    
    $('body').append(formHtml);
    $('#sf2Modal').modal('show');
    
    $('#sf2Form').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        
        showLoading();
        $('#sf2Modal').modal('hide');
        
        fetch('/dashboard/generate-sf2', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'SF2 report generated successfully');
                // Open download link
                window.open(data.data.download_url, '_blank');
            } else {
                showAlert('error', data.message || 'Failed to generate SF2 report');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'An error occurred while generating SF2 report');
        })
        .finally(() => {
            hideLoading();
            $('#sf2Modal').remove();
        });
    });
}

function generateSF4Report() {
    // Similar to SF2 but for SF4
    generateSF2Report(); // Reuse the same form for now
}

function loadTeacherReports() {
    showLoading();
    
    fetch('/dashboard/teacher-reports', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Teacher Reports:', data.data);
            // Handle teacher reports display
        } else {
            showAlert('error', 'Failed to load teacher reports');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while loading teacher reports');
    })
    .finally(() => {
        hideLoading();
    });
}

function loadParentNotifications() {
    showLoading();
    
    fetch('/dashboard/parent-notifications', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Parent Notifications:', data.data);
            // Handle parent notifications display
        } else {
            showAlert('error', 'Failed to load parent notifications');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while loading parent notifications');
    })
    .finally(() => {
        hideLoading();
    });
}

function exportDashboard(exportType, format) {
    showLoading();
    
    const formData = new FormData();
    formData.append('export_type', exportType);
    formData.append('format', format);
    
    fetch('/dashboard/export', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Dashboard data exported successfully');
            // Open download link
            window.open(data.data.download_url, '_blank');
        } else {
            showAlert('error', data.message || 'Failed to export dashboard data');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while exporting dashboard data');
    })
    .finally(() => {
        hideLoading();
    });
}
