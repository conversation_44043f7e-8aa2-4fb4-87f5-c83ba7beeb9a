<?php

namespace App\Console\Commands;

use App\Enums\QueueStatus;
use App\Enums\SmsStatus;
use App\Models\SmsQueue;
use App\Services\TextBeeSmsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessSmsQueue extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sms:process-queue 
                            {--batch-size=50 : Number of messages to process in one batch}
                            {--max-retries=3 : Maximum number of retries for failed messages}
                            {--delay=1 : Delay in seconds between messages}
                            {--priority=all : Process specific priority (1-10 or "all")}
                            {--dry-run : Show what would be processed without actually sending}';

    /**
     * The console command description.
     */
    protected $description = 'Process queued SMS messages';

    protected TextBeeSmsService $smsService;

    public function __construct(TextBeeSmsService $smsService)
    {
        parent::__construct();
        $this->smsService = $smsService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $batchSize = (int) $this->option('batch-size');
        $maxRetries = (int) $this->option('max-retries');
        $delay = (int) $this->option('delay');
        $priority = $this->option('priority');
        $dryRun = $this->option('dry-run');

        $this->info('Starting SMS queue processing...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No messages will actually be sent');
        }

        // Build query for pending messages
        $query = SmsQueue::where('status', QueueStatus::PENDING)
            ->where(function ($q) {
                $q->whereNull('scheduled_at')
                  ->orWhere('scheduled_at', '<=', now());
            })
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'asc');

        // Filter by priority if specified
        if ($priority !== 'all' && is_numeric($priority)) {
            $query->where('priority', (int) $priority);
        }

        $queuedMessages = $query->limit($batchSize)->get();

        if ($queuedMessages->isEmpty()) {
            $this->info('No messages in queue to process.');
            return self::SUCCESS;
        }

        $this->info("Found {$queuedMessages->count()} messages to process");

        $processed = 0;
        $successful = 0;
        $failed = 0;

        $progressBar = $this->output->createProgressBar($queuedMessages->count());
        $progressBar->start();

        foreach ($queuedMessages as $queueItem) {
            try {
                // Mark as processing
                if (!$dryRun) {
                    $queueItem->markAsProcessing();
                }

                $this->line("\nProcessing message ID: {$queueItem->id}");
                $this->line("To: {$queueItem->recipient_phone}");
                $this->line("Type: {$queueItem->message_type->value}");

                if ($dryRun) {
                    $this->line("Message: " . substr($queueItem->message_content, 0, 50) . '...');
                    $successful++;
                } else {
                    // Send the message
                    $result = $this->sendQueuedMessage($queueItem);
                    
                    if ($result['success']) {
                        $queueItem->update([
                            'status' => QueueStatus::PROCESSED,
                            'processed_at' => now(),
                            'message_id' => $result['message_id'] ?? null,
                            'provider_response' => $result['provider_response'] ?? [],
                        ]);
                        
                        $successful++;
                        $this->line("✓ Sent successfully");
                    } else {
                        $queueItem->increment('retry_count');
                        
                        if ($queueItem->retry_count >= $maxRetries) {
                            $queueItem->update([
                                'status' => QueueStatus::FAILED,
                                'failed_at' => now(),
                                'failure_reason' => $result['error'],
                            ]);
                            $this->line("✗ Failed permanently: {$result['error']}");
                        } else {
                            $queueItem->update([
                                'status' => QueueStatus::PENDING,
                                'next_retry_at' => now()->addSeconds($queueItem->retry_delay),
                                'failure_reason' => $result['error'],
                            ]);
                            $this->line("⚠ Failed, will retry: {$result['error']}");
                        }
                        
                        $failed++;
                    }
                }

                $processed++;
                $progressBar->advance();

                // Add delay between messages to respect rate limits
                if ($delay > 0 && !$dryRun) {
                    sleep($delay);
                }

            } catch (\Exception $e) {
                $this->error("Exception processing message {$queueItem->id}: {$e->getMessage()}");
                
                if (!$dryRun) {
                    $queueItem->update([
                        'status' => QueueStatus::FAILED,
                        'failed_at' => now(),
                        'failure_reason' => 'Exception: ' . $e->getMessage(),
                    ]);
                }
                
                $failed++;
                $processed++;
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display summary
        $this->info('SMS Queue Processing Complete');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Processed', $processed],
                ['Successful', $successful],
                ['Failed', $failed],
                ['Success Rate', $processed > 0 ? round(($successful / $processed) * 100, 2) . '%' : '0%'],
            ]
        );

        // Log summary
        Log::info('SMS queue processing completed', [
            'processed' => $processed,
            'successful' => $successful,
            'failed' => $failed,
            'batch_size' => $batchSize,
            'dry_run' => $dryRun,
        ]);

        return self::SUCCESS;
    }

    /**
     * Send a queued message.
     */
    protected function sendQueuedMessage(SmsQueue $queueItem): array
    {
        try {
            // Use the SMS service to send the message
            $result = $this->smsService->sendSMS(
                phone: $queueItem->recipient_phone,
                message: $queueItem->message_content,
                type: $queueItem->message_type,
                teacherId: $queueItem->teacher_id,
                recipientName: $queueItem->recipient_name,
                recipientType: $queueItem->recipient_type,
                batchId: $queueItem->batch_id,
                templateUsed: $queueItem->template_used,
                metadata: $queueItem->metadata ?? []
            );

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
