<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title ?? 'Attendance Report' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-info {
            font-size: 11px;
            color: #666;
        }
        
        .section-header {
            background-color: #f5f5f5;
            padding: 8px;
            font-weight: bold;
            border: 1px solid #ddd;
            margin-top: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        
        th {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        
        .status-present {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-absent {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-late {
            color: #ffc107;
            font-weight: bold;
        }
        
        .status-excused {
            color: #17a2b8;
            font-weight: bold;
        }
        
        .summary-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-number {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-label {
            font-size: 10px;
            color: #666;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="school-name">{{ $school_name ?? config('app.name', 'School Name') }}</div>
        <div class="report-title">{{ $title ?? 'Attendance Report' }}</div>
        <div class="report-info">
            @if(isset($grade_level))
                Grade {{ $grade_level }}
            @endif
            @if(isset($section))
                - Section {{ $section }}
            @endif
            @if(isset($subject))
                - {{ $subject }}
            @endif
            <br>
            @if(isset($date))
                Date: {{ $date }}
            @elseif(isset($date_from) && isset($date_to))
                Period: {{ $date_from }} to {{ $date_to }}
            @endif
            <br>
            Generated on: {{ now()->format('F d, Y h:i A') }}
        </div>
    </div>

    <!-- Summary Section -->
    @if(isset($summary))
        <div class="summary-box">
            <div class="section-header">Attendance Summary</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number status-present">{{ $summary['present_count'] ?? 0 }}</div>
                    <div class="summary-label">Present</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number status-absent">{{ $summary['absent_count'] ?? 0 }}</div>
                    <div class="summary-label">Absent</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number status-late">{{ $summary['late_count'] ?? 0 }}</div>
                    <div class="summary-label">Late</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number status-excused">{{ $summary['excused_count'] ?? 0 }}</div>
                    <div class="summary-label">Excused</div>
                </div>
            </div>
        </div>
    @endif

    <!-- Daily Attendance Table -->
    @if(isset($attendance_data) && $attendance_data->count() > 0)
        <div class="section-header">Attendance Records</div>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Student ID</th>
                    <th>Student Name</th>
                    <th>Status</th>
                    <th>Time In</th>
                    <th>Time Out</th>
                    <th>Duration</th>
                    <th>Remarks</th>
                </tr>
            </thead>
            <tbody>
                @foreach($attendance_data as $record)
                    <tr>
                        <td>{{ $record->date->format('M d, Y') }}</td>
                        <td>{{ $record->student->student_id ?? '' }}</td>
                        <td>{{ $record->student->full_name ?? '' }}</td>
                        <td class="status-{{ $record->status->value }}">
                            {{ $record->status->label() }}
                        </td>
                        <td>{{ $record->formatted_time_in ?? '-' }}</td>
                        <td>{{ $record->formatted_time_out ?? '-' }}</td>
                        <td>{{ $record->duration ?? '-' }}</td>
                        <td>{{ $record->remarks ?? '-' }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    <!-- SF2 Format -->
    @if(isset($students_data))
        <div class="section-header">Daily Attendance Record (SF2)</div>
        <table>
            <thead>
                <tr>
                    <th rowspan="2">Student Name</th>
                    <th rowspan="2">Student ID</th>
                    @foreach($date_range as $date)
                        <th>{{ $date->format('M d') }}</th>
                    @endforeach
                    <th rowspan="2">Total Present</th>
                    <th rowspan="2">Total Absent</th>
                    <th rowspan="2">Attendance Rate</th>
                </tr>
                <tr>
                    @foreach($date_range as $date)
                        <th>{{ $date->format('D') }}</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                @foreach($students_data as $studentData)
                    <tr>
                        <td>{{ $studentData['student']->full_name }}</td>
                        <td>{{ $studentData['student']->student_id }}</td>
                        @foreach($studentData['daily_attendance'] as $dailyRecord)
                            <td class="status-{{ $dailyRecord['status']?->value ?? 'absent' }}">
                                @if($dailyRecord['status'])
                                    {{ substr($dailyRecord['status']->value, 0, 1) }}
                                @else
                                    A
                                @endif
                            </td>
                        @endforeach
                        <td>{{ $studentData['summary']['present_count'] + $studentData['summary']['late_count'] }}</td>
                        <td>{{ $studentData['summary']['absent_count'] }}</td>
                        <td>
                            @php
                                $total = $studentData['summary']['total_days'];
                                $present = $studentData['summary']['present_count'] + $studentData['summary']['late_count'];
                                $rate = $total > 0 ? round(($present / $total) * 100, 1) : 0;
                            @endphp
                            {{ $rate }}%
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Legend -->
        <div style="margin-top: 15px; font-size: 10px;">
            <strong>Legend:</strong>
            <span class="status-present">P = Present</span> |
            <span class="status-absent">A = Absent</span> |
            <span class="status-late">L = Late</span> |
            <span class="status-excused">E = Excused</span>
        </div>
    @endif

    <!-- Students Summary (SF4) -->
    @if(isset($students_summary))
        <div class="section-header">Monthly Attendance Summary (SF4)</div>
        <table>
            <thead>
                <tr>
                    <th>Student Name</th>
                    <th>Student ID</th>
                    <th>Total Days</th>
                    <th>Present</th>
                    <th>Absent</th>
                    <th>Late</th>
                    <th>Excused</th>
                    <th>Attendance Rate</th>
                </tr>
            </thead>
            <tbody>
                @foreach($students_summary as $studentSummary)
                    <tr>
                        <td>{{ $studentSummary['student']->full_name }}</td>
                        <td>{{ $studentSummary['student']->student_id }}</td>
                        <td>{{ $studentSummary['summary']['total_days'] }}</td>
                        <td class="status-present">{{ $studentSummary['summary']['present_count'] }}</td>
                        <td class="status-absent">{{ $studentSummary['summary']['absent_count'] }}</td>
                        <td class="status-late">{{ $studentSummary['summary']['late_count'] }}</td>
                        <td class="status-excused">{{ $studentSummary['summary']['excused_count'] }}</td>
                        <td>
                            @php
                                $total = $studentSummary['summary']['total_days'];
                                $present = $studentSummary['summary']['present_count'] + $studentSummary['summary']['late_count'];
                                $rate = $total > 0 ? round(($present / $total) * 100, 1) : 0;
                            @endphp
                            {{ $rate }}%
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        @if(isset($class_summary))
            <div class="summary-box">
                <div class="section-header">Class Summary</div>
                <p><strong>Total Students:</strong> {{ $class_summary['total_students'] }}</p>
                <p><strong>Total Present Days:</strong> {{ $class_summary['total_present'] }}</p>
                <p><strong>Total Absent Days:</strong> {{ $class_summary['total_absent'] }}</p>
                <p><strong>Total Late Days:</strong> {{ $class_summary['total_late'] }}</p>
                <p><strong>Total Excused Days:</strong> {{ $class_summary['total_excused'] }}</p>
            </div>
        @endif
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This report was generated automatically by the QR Student Attendance Management System.</p>
        <p>For questions or concerns, please contact the school administration.</p>
    </div>
</body>
</html>
