<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'sms' => [
        'provider' => env('SMS_PROVIDER', 'log'), // log, textbee, semaphore, twilio, itexmo

        'textbee' => [
            'api_key' => env('TEXTBEE_API_KEY'),
            'sender_name' => env('TEXTBEE_SENDER_NAME', 'SCHOOL'),
            'api_url' => env('TEXTBEE_API_URL', 'https://api.textbee.ph/api/v1'),
            'webhook_url' => env('TEXTBEE_WEBHOOK_URL'),
            'rate_limit' => env('TEXTBEE_RATE_LIMIT', 60), // messages per minute
            'cost_per_sms' => env('TEXTBEE_COST_PER_SMS', 0.50), // PHP per SMS
            'max_retries' => env('TEXTBEE_MAX_RETRIES', 3),
            'retry_delay' => env('TEXTBEE_RETRY_DELAY', 300), // seconds
        ],

        'semaphore' => [
            'api_key' => env('SEMAPHORE_API_KEY'),
            'sender_name' => env('SEMAPHORE_SENDER_NAME', 'SCHOOL'),
        ],

        'twilio' => [
            'account_sid' => env('TWILIO_ACCOUNT_SID'),
            'auth_token' => env('TWILIO_AUTH_TOKEN'),
            'from_number' => env('TWILIO_FROM_NUMBER'),
        ],

        'itexmo' => [
            'api_code' => env('ITEXMO_API_CODE'),
            'password' => env('ITEXMO_PASSWORD'),
        ],

        // Global SMS settings
        'default_rate_limit' => env('SMS_DEFAULT_RATE_LIMIT', 100), // per hour
        'enable_queue' => env('SMS_ENABLE_QUEUE', true),
        'enable_rate_limiting' => env('SMS_ENABLE_RATE_LIMITING', true),
        'enable_cost_tracking' => env('SMS_ENABLE_COST_TRACKING', true),
        'max_message_length' => env('SMS_MAX_MESSAGE_LENGTH', 1600),
        'default_template_category' => env('SMS_DEFAULT_TEMPLATE_CATEGORY', 'general'),
    ],

];
