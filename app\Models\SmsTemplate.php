<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class SmsTemplate extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'sms_templates';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'content',
        'variables',
        'sample_output',
        'category',
        'type',
        'is_active',
        'character_limit',
        'usage_count',
        'created_by',
        'is_system',
        'allowed_roles',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'variables' => 'array',
            'is_active' => 'boolean',
            'character_limit' => 'integer',
            'usage_count' => 'integer',
            'is_system' => 'boolean',
            'allowed_roles' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // RELATIONSHIPS

    /**
     * Get the teacher who created the template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Teacher::class, 'created_by');
    }

    // SCOPES

    /**
     * Scope to only include active templates.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeInCategory(Builder $query, string $category): void
    {
        $query->where('category', $category);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeOfType(Builder $query, string $type): void
    {
        $query->where('type', $type);
    }

    /**
     * Scope to filter system templates.
     */
    public function scopeSystem(Builder $query): void
    {
        $query->where('is_system', true);
    }

    /**
     * Scope to filter user templates.
     */
    public function scopeUser(Builder $query): void
    {
        $query->where('is_system', false);
    }

    /**
     * Scope to filter templates accessible by role.
     */
    public function scopeAccessibleByRole(Builder $query, string $role): void
    {
        $query->where(function ($q) use ($role) {
            $q->whereNull('allowed_roles')
              ->orWhereJsonContains('allowed_roles', $role);
        });
    }

    // ACCESSORS & MUTATORS

    /**
     * Get available variables as a formatted string.
     */
    public function getVariableListAttribute(): string
    {
        if (!$this->variables) {
            return 'None';
        }
        
        return collect($this->variables)
            ->map(fn($var) => "{{$var}}")
            ->join(', ');
    }

    /**
     * Get character count of template content.
     */
    public function getCharacterCountAttribute(): int
    {
        return strlen($this->content);
    }

    /**
     * Check if template is within character limit.
     */
    public function getIsWithinLimitAttribute(): bool
    {
        return $this->character_count <= $this->character_limit;
    }

    // METHODS

    /**
     * Render template with provided variables.
     */
    public function render(array $variables = []): string
    {
        $content = $this->content;
        
        foreach ($variables as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }
        
        return $content;
    }

    /**
     * Validate template variables.
     */
    public function validateVariables(array $variables): array
    {
        $errors = [];
        $requiredVars = $this->variables ?? [];
        
        foreach ($requiredVars as $var) {
            if (!isset($variables[$var]) || empty($variables[$var])) {
                $errors[] = "Variable '{$var}' is required";
            }
        }
        
        return $errors;
    }

    /**
     * Get missing variables from provided data.
     */
    public function getMissingVariables(array $variables): array
    {
        $requiredVars = $this->variables ?? [];
        $providedVars = array_keys($variables);
        
        return array_diff($requiredVars, $providedVars);
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * Check if template can be deleted.
     */
    public function canDelete(): bool
    {
        return !$this->is_system;
    }

    /**
     * Get sample rendered output.
     */
    public function getSampleOutput(): string
    {
        if ($this->sample_output) {
            return $this->sample_output;
        }
        
        // Generate sample output with placeholder values
        $sampleVars = [];
        foreach ($this->variables ?? [] as $var) {
            $sampleVars[$var] = match($var) {
                'student_name' => 'Juan Dela Cruz',
                'parent_name' => 'Maria Dela Cruz',
                'date' => now()->format('M d, Y'),
                'time' => now()->format('h:i A'),
                'subject' => 'Mathematics',
                'teacher_name' => 'Ms. Santos',
                'school_name' => 'Sample High School',
                'status' => 'Present',
                default => ucwords(str_replace('_', ' ', $var)),
            };
        }
        
        return $this->render($sampleVars);
    }
}
