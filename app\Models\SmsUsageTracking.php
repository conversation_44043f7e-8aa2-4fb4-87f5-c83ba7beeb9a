<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class SmsUsageTracking extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'sms_usage_tracking';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'teacher_id',
        'tracking_date',
        'tracking_period',
        'messages_sent',
        'messages_delivered',
        'messages_failed',
        'messages_pending',
        'total_cost',
        'average_cost_per_message',
        'attendance_messages',
        'bulk_messages',
        'manual_messages',
        'alert_messages',
        'provider_usage',
        'rate_limit_hits',
        'last_rate_limit_hit',
        'average_delivery_time',
        'success_rate',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'tracking_date' => 'date',
            'messages_sent' => 'integer',
            'messages_delivered' => 'integer',
            'messages_failed' => 'integer',
            'messages_pending' => 'integer',
            'total_cost' => 'decimal:4',
            'average_cost_per_message' => 'decimal:4',
            'attendance_messages' => 'integer',
            'bulk_messages' => 'integer',
            'manual_messages' => 'integer',
            'alert_messages' => 'integer',
            'provider_usage' => 'array',
            'rate_limit_hits' => 'integer',
            'last_rate_limit_hit' => 'datetime',
            'average_delivery_time' => 'decimal:2',
            'success_rate' => 'decimal:2',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // RELATIONSHIPS

    /**
     * Get the teacher this tracking belongs to.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    // SCOPES

    /**
     * Scope to filter by teacher.
     */
    public function scopeForTeacher(Builder $query, int $teacherId): void
    {
        $query->where('teacher_id', $teacherId);
    }

    /**
     * Scope to filter by tracking period.
     */
    public function scopeForPeriod(Builder $query, string $period): void
    {
        $query->where('tracking_period', $period);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange(Builder $query, $startDate, $endDate): void
    {
        $query->whereBetween('tracking_date', [$startDate, $endDate]);
    }

    /**
     * Scope for current month.
     */
    public function scopeCurrentMonth(Builder $query): void
    {
        $query->whereMonth('tracking_date', now()->month)
              ->whereYear('tracking_date', now()->year);
    }

    /**
     * Scope for current week.
     */
    public function scopeCurrentWeek(Builder $query): void
    {
        $query->whereBetween('tracking_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    // ACCESSORS & MUTATORS

    /**
     * Get total messages count.
     */
    public function getTotalMessagesAttribute(): int
    {
        return $this->messages_sent + $this->messages_pending;
    }

    /**
     * Get formatted total cost.
     */
    public function getFormattedTotalCostAttribute(): string
    {
        return '₱' . number_format($this->total_cost, 2);
    }

    /**
     * Get formatted average cost.
     */
    public function getFormattedAverageCostAttribute(): string
    {
        return '₱' . number_format($this->average_cost_per_message, 2);
    }

    /**
     * Get success rate percentage.
     */
    public function getSuccessRatePercentageAttribute(): string
    {
        return number_format($this->success_rate, 1) . '%';
    }

    /**
     * Get failure rate.
     */
    public function getFailureRateAttribute(): float
    {
        return 100 - $this->success_rate;
    }

    // METHODS

    /**
     * Update usage statistics.
     */
    public function updateStats(array $stats): void
    {
        $this->update($stats);
        $this->recalculateAverages();
    }

    /**
     * Recalculate average values.
     */
    public function recalculateAverages(): void
    {
        $totalMessages = $this->messages_sent;
        
        if ($totalMessages > 0) {
            $this->update([
                'average_cost_per_message' => $this->total_cost / $totalMessages,
                'success_rate' => ($this->messages_delivered / $totalMessages) * 100,
            ]);
        }
    }

    /**
     * Add message to tracking.
     */
    public function addMessage(string $type, float $cost, bool $delivered = false): void
    {
        $updates = [
            'messages_sent' => $this->messages_sent + 1,
            'total_cost' => $this->total_cost + $cost,
        ];

        if ($delivered) {
            $updates['messages_delivered'] = $this->messages_delivered + 1;
        }

        // Update message type counters
        switch ($type) {
            case 'attendance':
                $updates['attendance_messages'] = $this->attendance_messages + 1;
                break;
            case 'bulk':
                $updates['bulk_messages'] = $this->bulk_messages + 1;
                break;
            case 'manual':
                $updates['manual_messages'] = $this->manual_messages + 1;
                break;
            case 'alert':
                $updates['alert_messages'] = $this->alert_messages + 1;
                break;
        }

        $this->update($updates);
        $this->recalculateAverages();
    }

    /**
     * Record rate limit hit.
     */
    public function recordRateLimitHit(): void
    {
        $this->update([
            'rate_limit_hits' => $this->rate_limit_hits + 1,
            'last_rate_limit_hit' => now(),
        ]);
    }

    /**
     * Get usage summary.
     */
    public function getSummary(): array
    {
        return [
            'total_messages' => $this->total_messages,
            'messages_sent' => $this->messages_sent,
            'messages_delivered' => $this->messages_delivered,
            'messages_failed' => $this->messages_failed,
            'success_rate' => $this->success_rate,
            'total_cost' => $this->total_cost,
            'average_cost' => $this->average_cost_per_message,
            'by_type' => [
                'attendance' => $this->attendance_messages,
                'bulk' => $this->bulk_messages,
                'manual' => $this->manual_messages,
                'alert' => $this->alert_messages,
            ],
            'provider_usage' => $this->provider_usage ?? [],
        ];
    }
}
