<?php

namespace App\Enums;

enum SmsStatus: string
{
    case PENDING = 'pending';
    case QUEUED = 'queued';
    case SENT = 'sent';
    case DELIVERED = 'delivered';
    case FAILED = 'failed';
    case EXPIRED = 'expired';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pending',
            self::QUEUED => 'Queued',
            self::SENT => 'Sent',
            self::DELIVERED => 'Delivered',
            self::FAILED => 'Failed',
            self::EXPIRED => 'Expired',
        };
    }

    /**
     * Get status color for UI.
     */
    public function color(): string
    {
        return match($this) {
            self::PENDING => 'yellow',
            self::QUEUED => 'blue',
            self::SENT => 'indigo',
            self::DELIVERED => 'green',
            self::FAILED => 'red',
            self::EXPIRED => 'gray',
        };
    }

    /**
     * Check if status indicates success.
     */
    public function isSuccess(): bool
    {
        return in_array($this, [self::SENT, self::DELIVERED]);
    }

    /**
     * Check if status indicates failure.
     */
    public function isFailure(): bool
    {
        return in_array($this, [self::FAILED, self::EXPIRED]);
    }

    /**
     * Check if status is in progress.
     */
    public function isInProgress(): bool
    {
        return in_array($this, [self::PENDING, self::QUEUED]);
    }

    /**
     * Check if status is final (no more changes expected).
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::DELIVERED, self::FAILED, self::EXPIRED]);
    }
}
