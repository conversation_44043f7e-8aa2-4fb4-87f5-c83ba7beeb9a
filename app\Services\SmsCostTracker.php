<?php

namespace App\Services;

use App\Enums\SmsProvider;
use App\Enums\SmsType;
use App\Models\SmsLog;
use App\Models\SmsUsageTracking;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class SmsCostTracker
{
    /**
     * Calculate cost for a message.
     */
    public function calculateMessageCost(
        string $message,
        SmsProvider $provider = SmsProvider::TEXTBEE
    ): array {
        $characterCount = strlen($message);
        $parts = $this->calculateMessageParts($characterCount);
        $costPerSms = $this->getProviderCostPerSms($provider);
        $totalCost = $parts * $costPerSms;

        return [
            'character_count' => $characterCount,
            'message_parts' => $parts,
            'cost_per_sms' => $costPerSms,
            'total_cost' => $totalCost,
            'provider' => $provider,
        ];
    }

    /**
     * Calculate bulk SMS cost.
     */
    public function calculateBulkCost(
        array $messages,
        SmsProvider $provider = SmsProvider::TEXTBEE
    ): array {
        $totalCost = 0;
        $totalParts = 0;
        $totalMessages = count($messages);
        $costPerSms = $this->getProviderCostPerSms($provider);
        $breakdown = [];

        foreach ($messages as $index => $message) {
            $characterCount = strlen($message);
            $parts = $this->calculateMessageParts($characterCount);
            $cost = $parts * $costPerSms;
            
            $totalCost += $cost;
            $totalParts += $parts;
            
            $breakdown[] = [
                'index' => $index,
                'character_count' => $characterCount,
                'parts' => $parts,
                'cost' => $cost,
            ];
        }

        return [
            'total_messages' => $totalMessages,
            'total_parts' => $totalParts,
            'total_cost' => $totalCost,
            'average_cost_per_message' => $totalMessages > 0 ? $totalCost / $totalMessages : 0,
            'cost_per_sms' => $costPerSms,
            'provider' => $provider,
            'breakdown' => $breakdown,
        ];
    }

    /**
     * Track SMS cost for a teacher.
     */
    public function trackCost(
        int $teacherId,
        float $cost,
        SmsType $messageType,
        SmsProvider $provider,
        bool $delivered = false,
        ?Carbon $date = null
    ): void {
        $date = $date ?? now();
        
        $tracking = SmsUsageTracking::firstOrCreate([
            'teacher_id' => $teacherId,
            'tracking_date' => $date->toDateString(),
            'tracking_period' => 'daily',
        ]);

        $tracking->addMessage($messageType->value, $cost, $delivered);
        
        // Update provider usage
        $providerUsage = $tracking->provider_usage ?? [];
        $providerUsage[$provider->value] = ($providerUsage[$provider->value] ?? 0) + 1;
        $tracking->update(['provider_usage' => $providerUsage]);
    }

    /**
     * Get cost summary for a teacher.
     */
    public function getTeacherCostSummary(
        int $teacherId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): array {
        $startDate = $startDate ?? now()->subDays(30);
        $endDate = $endDate ?? now();

        $tracking = SmsUsageTracking::forTeacher($teacherId)
            ->inDateRange($startDate, $endDate)
            ->get();

        $totalCost = $tracking->sum('total_cost');
        $totalMessages = $tracking->sum('messages_sent');
        $totalDelivered = $tracking->sum('messages_delivered');
        $totalFailed = $tracking->sum('messages_failed');

        // Message type breakdown
        $messageTypes = [
            'attendance' => $tracking->sum('attendance_messages'),
            'bulk' => $tracking->sum('bulk_messages'),
            'manual' => $tracking->sum('manual_messages'),
            'alert' => $tracking->sum('alert_messages'),
        ];

        // Daily breakdown
        $dailyBreakdown = $tracking->groupBy('tracking_date')
            ->map(function ($group, $date) {
                return [
                    'date' => $date,
                    'cost' => $group->sum('total_cost'),
                    'messages' => $group->sum('messages_sent'),
                    'delivered' => $group->sum('messages_delivered'),
                    'success_rate' => $group->avg('success_rate'),
                ];
            })
            ->values();

        return [
            'teacher_id' => $teacherId,
            'period' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
            ],
            'summary' => [
                'total_cost' => $totalCost,
                'total_messages' => $totalMessages,
                'total_delivered' => $totalDelivered,
                'total_failed' => $totalFailed,
                'average_cost_per_message' => $totalMessages > 0 ? $totalCost / $totalMessages : 0,
                'success_rate' => $totalMessages > 0 ? ($totalDelivered / $totalMessages) * 100 : 0,
            ],
            'message_types' => $messageTypes,
            'daily_breakdown' => $dailyBreakdown,
        ];
    }

    /**
     * Get school-wide cost summary.
     */
    public function getSchoolCostSummary(
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): array {
        $startDate = $startDate ?? now()->subDays(30);
        $endDate = $endDate ?? now();

        $tracking = SmsUsageTracking::inDateRange($startDate, $endDate)->get();
        
        $totalCost = $tracking->sum('total_cost');
        $totalMessages = $tracking->sum('messages_sent');
        $totalDelivered = $tracking->sum('messages_delivered');
        $totalFailed = $tracking->sum('messages_failed');

        // Top spending teachers
        $topTeachers = $tracking->groupBy('teacher_id')
            ->map(function ($group) {
                return [
                    'teacher_id' => $group->first()->teacher_id,
                    'teacher' => $group->first()->teacher,
                    'total_cost' => $group->sum('total_cost'),
                    'total_messages' => $group->sum('messages_sent'),
                    'success_rate' => $group->avg('success_rate'),
                ];
            })
            ->sortByDesc('total_cost')
            ->take(10)
            ->values();

        // Provider usage and costs
        $providerStats = [];
        foreach ($tracking as $record) {
            $usage = $record->provider_usage ?? [];
            foreach ($usage as $provider => $count) {
                if (!isset($providerStats[$provider])) {
                    $providerStats[$provider] = [
                        'usage_count' => 0,
                        'estimated_cost' => 0,
                    ];
                }
                $providerStats[$provider]['usage_count'] += $count;
                $providerStats[$provider]['estimated_cost'] += $count * $this->getProviderCostPerSms(SmsProvider::from($provider));
            }
        }

        // Monthly trend
        $monthlyTrend = $tracking->groupBy(function ($item) {
            return Carbon::parse($item->tracking_date)->format('Y-m');
        })->map(function ($group, $month) {
            return [
                'month' => $month,
                'cost' => $group->sum('total_cost'),
                'messages' => $group->sum('messages_sent'),
                'success_rate' => $group->avg('success_rate'),
            ];
        })->values();

        return [
            'period' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
            ],
            'summary' => [
                'total_cost' => $totalCost,
                'total_messages' => $totalMessages,
                'total_delivered' => $totalDelivered,
                'total_failed' => $totalFailed,
                'average_cost_per_message' => $totalMessages > 0 ? $totalCost / $totalMessages : 0,
                'success_rate' => $totalMessages > 0 ? ($totalDelivered / $totalMessages) * 100 : 0,
                'active_teachers' => $tracking->pluck('teacher_id')->unique()->count(),
            ],
            'top_teachers' => $topTeachers,
            'provider_stats' => $providerStats,
            'monthly_trend' => $monthlyTrend,
        ];
    }

    /**
     * Get cost projections based on usage patterns.
     */
    public function getCostProjections(
        int $teacherId,
        int $projectionDays = 30
    ): array {
        // Get historical data for the last 30 days
        $historicalData = SmsUsageTracking::forTeacher($teacherId)
            ->inDateRange(now()->subDays(30), now())
            ->get();

        if ($historicalData->isEmpty()) {
            return [
                'projected_cost' => 0,
                'projected_messages' => 0,
                'confidence' => 'low',
                'based_on_days' => 0,
            ];
        }

        $avgDailyCost = $historicalData->avg('total_cost');
        $avgDailyMessages = $historicalData->avg('messages_sent');
        
        $projectedCost = $avgDailyCost * $projectionDays;
        $projectedMessages = $avgDailyMessages * $projectionDays;
        
        // Determine confidence based on data consistency
        $costVariance = $this->calculateVariance($historicalData->pluck('total_cost')->toArray());
        $confidence = $costVariance < 0.3 ? 'high' : ($costVariance < 0.6 ? 'medium' : 'low');

        return [
            'projected_cost' => round($projectedCost, 2),
            'projected_messages' => round($projectedMessages),
            'daily_average_cost' => round($avgDailyCost, 2),
            'daily_average_messages' => round($avgDailyMessages),
            'confidence' => $confidence,
            'based_on_days' => $historicalData->count(),
            'projection_period' => $projectionDays,
        ];
    }

    /**
     * Calculate message parts based on character count.
     */
    protected function calculateMessageParts(int $characterCount): int
    {
        if ($characterCount <= 160) {
            return 1;
        }
        
        return ceil($characterCount / 153); // 153 chars per part for multi-part SMS
    }

    /**
     * Get cost per SMS for provider.
     */
    protected function getProviderCostPerSms(SmsProvider $provider): float
    {
        return match($provider) {
            SmsProvider::TEXTBEE => config('services.sms.textbee.cost_per_sms', 0.50),
            SmsProvider::SEMAPHORE => config('services.sms.semaphore.cost_per_sms', 2.50),
            SmsProvider::TWILIO => config('services.sms.twilio.cost_per_sms', 3.00),
            SmsProvider::ITEXMO => config('services.sms.itexmo.cost_per_sms', 1.00),
            SmsProvider::LOG => 0.00,
        };
    }

    /**
     * Calculate variance for confidence assessment.
     */
    protected function calculateVariance(array $values): float
    {
        if (count($values) < 2) {
            return 0;
        }

        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);

        return $mean > 0 ? sqrt($variance) / $mean : 0;
    }
}
