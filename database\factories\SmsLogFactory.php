<?php

namespace Database\Factories;

use App\Enums\SmsProvider;
use App\Enums\SmsStatus;
use App\Enums\SmsType;
use App\Models\SmsLog;
use App\Models\Teacher;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SmsLog>
 */
class SmsLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = SmsLog::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'teacher_id' => Teacher::factory(),
            'recipient_phone' => $this->faker->randomElement([
                '+639123456789',
                '+639987654321',
                '+639555123456',
                '+639777888999',
            ]),
            'recipient_name' => $this->faker->name(),
            'recipient_type' => $this->faker->randomElement(['parent', 'student', 'teacher']),
            'message_content' => $this->faker->sentence(10),
            'message_type' => $this->faker->randomElement(SmsType::cases()),
            'status' => $this->faker->randomElement(SmsStatus::cases()),
            'provider' => $this->faker->randomElement(SmsProvider::cases()),
            'message_id' => 'msg_' . $this->faker->uuid(),
            'batch_id' => $this->faker->optional()->uuid(),
            'template_used' => $this->faker->optional()->randomElement([
                'attendance_absent',
                'attendance_late',
                'attendance_present',
                'emergency_alert',
            ]),
            'cost_per_sms' => $this->faker->randomFloat(2, 0.50, 2.00),
            'total_cost' => $this->faker->randomFloat(2, 0.50, 5.00),
            'character_count' => $this->faker->numberBetween(50, 300),
            'sms_parts' => $this->faker->numberBetween(1, 3),
            'retry_count' => $this->faker->numberBetween(0, 3),
            'sent_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'failed_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'last_retry_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'failure_reason' => $this->faker->optional()->sentence(),
            'provider_response' => $this->faker->optional()->randomElement([
                ['status' => 'success', 'code' => 200],
                ['status' => 'failed', 'code' => 400, 'error' => 'Invalid phone number'],
                ['status' => 'delivered', 'delivered_at' => now()->toISOString()],
            ]),
            'metadata' => $this->faker->optional()->randomElement([
                ['student_id' => 1, 'subject_id' => 1],
                ['attendance_id' => 1, 'class_id' => 1],
                ['event_type' => 'emergency', 'priority' => 'high'],
            ]),
        ];
    }

    /**
     * Indicate that the SMS is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SmsStatus::PENDING,
            'sent_at' => null,
            'delivered_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the SMS is sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SmsStatus::SENT,
            'sent_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'delivered_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the SMS is delivered.
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SmsStatus::DELIVERED,
            'sent_at' => $this->faker->dateTimeBetween('-1 day', '-1 hour'),
            'delivered_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the SMS failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SmsStatus::FAILED,
            'sent_at' => null,
            'delivered_at' => null,
            'failed_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'failure_reason' => $this->faker->randomElement([
                'Invalid phone number',
                'Network error',
                'Insufficient balance',
                'Message blocked',
            ]),
        ]);
    }

    /**
     * Indicate that the SMS is for attendance.
     */
    public function attendance(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_type' => SmsType::ATTENDANCE,
            'template_used' => $this->faker->randomElement([
                'attendance_absent',
                'attendance_late',
                'attendance_present',
            ]),
            'metadata' => [
                'student_id' => $this->faker->numberBetween(1, 100),
                'attendance_id' => $this->faker->numberBetween(1, 1000),
                'subject_id' => $this->faker->numberBetween(1, 20),
            ],
        ]);
    }

    /**
     * Indicate that the SMS is bulk.
     */
    public function bulk(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_type' => SmsType::BULK,
            'batch_id' => $this->faker->uuid(),
            'metadata' => [
                'batch_size' => $this->faker->numberBetween(10, 100),
                'campaign_name' => $this->faker->words(3, true),
            ],
        ]);
    }

    /**
     * Indicate that the SMS is retryable.
     */
    public function retryable(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SmsStatus::FAILED,
            'retry_count' => $this->faker->numberBetween(0, 2), // Less than max retries
            'failed_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'failure_reason' => 'Network timeout',
        ]);
    }

    /**
     * Indicate that the SMS has exceeded retry limit.
     */
    public function exhaustedRetries(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SmsStatus::FAILED,
            'retry_count' => 5, // Exceeded max retries
            'failed_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'failure_reason' => 'Max retries exceeded',
        ]);
    }

    /**
     * Indicate that the SMS is expensive (multi-part).
     */
    public function multiPart(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_content' => $this->faker->text(500), // Long message
            'character_count' => $this->faker->numberBetween(300, 500),
            'sms_parts' => $this->faker->numberBetween(2, 4),
            'total_cost' => $this->faker->randomFloat(2, 2.00, 8.00),
        ]);
    }
}
