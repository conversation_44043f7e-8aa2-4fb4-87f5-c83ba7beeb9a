<?php

use App\Http\Controllers\SmsController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// SMS API Routes
Route::middleware(['auth:sanctum'])->prefix('sms')->name('api.sms.')->group(function () {
    // Send SMS
    Route::post('/send', [SmsController::class, 'sendSms'])->name('send');
    Route::post('/send-bulk', [SmsController::class, 'sendBulkSms'])->name('send-bulk');
    
    // Status and Tracking
    Route::get('/status/{messageId}', [SmsController::class, 'getDeliveryStatus'])->name('status');
    Route::get('/logs', [SmsController::class, 'getLogs'])->name('logs');
    Route::get('/queue', [SmsController::class, 'getQueueStatus'])->name('queue');
    
    // Management
    Route::post('/retry-failed', [SmsController::class, 'retryFailed'])->name('retry-failed');
    Route::get('/report', [SmsController::class, 'generateReport'])->name('report');
    Route::get('/cost-summary', [SmsController::class, 'getCostSummary'])->name('cost-summary');
    
    // Templates and Utilities
    Route::get('/templates', [SmsController::class, 'getTemplates'])->name('templates');
    Route::get('/templates/{templateName}/preview', [SmsController::class, 'previewTemplate'])->name('template-preview');
    Route::post('/validate-phone', [SmsController::class, 'validatePhone'])->name('validate-phone');
});

// SMS Web Routes (with rate limiting middleware)
Route::middleware(['auth', 'sms.rate.limit:teacher,60,minute'])->prefix('sms')->name('sms.')->group(function () {
    // Send SMS
    Route::post('/send', [SmsController::class, 'sendSms'])->name('send');
    Route::post('/send-bulk', [SmsController::class, 'sendBulkSms'])->name('send-bulk');
    
    // Status and Tracking
    Route::get('/status/{messageId}', [SmsController::class, 'getDeliveryStatus'])->name('status');
    Route::get('/logs', [SmsController::class, 'getLogs'])->name('logs');
    Route::get('/queue', [SmsController::class, 'getQueueStatus'])->name('queue');
    
    // Management
    Route::post('/retry-failed', [SmsController::class, 'retryFailed'])->name('retry-failed');
    Route::get('/report', [SmsController::class, 'generateReport'])->name('report');
    Route::get('/cost-summary', [SmsController::class, 'getCostSummary'])->name('cost-summary');
    
    // Templates and Utilities
    Route::get('/templates', [SmsController::class, 'getTemplates'])->name('templates');
    Route::get('/templates/{templateName}/preview', [SmsController::class, 'previewTemplate'])->name('template-preview');
    Route::post('/validate-phone', [SmsController::class, 'validatePhone'])->name('validate-phone');
});
