<?php

namespace App\Services;

use App\Models\Student;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Label\LabelAlignment;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;

class QRCodeService
{
    /**
     * Get QR code expiration time in minutes from config
     */
    private function getExpirationMinutes(): int
    {
        return config('qrcode.expiration_minutes', 60);
    }

    /**
     * Get maximum batch size from config
     */
    private function getMaxBatchSize(): int
    {
        return config('qrcode.max_batch_size', 100);
    }

    /**
     * Generate unique QR code for student with encryption.
     *
     * @param Student $student
     * @param array $options Additional options for QR generation
     * @return string Path to generated QR code file
     * @throws Exception
     */
    public function generateStudentQR(Student $student, array $options = []): string
    {
        try {
            Log::info('Generating QR code for student', ['student_id' => $student->student_id]);

            // Create encrypted payload with time-based validation
            $payload = $this->createSecurePayload($student);

            // Generate QR code image
            $filename = $this->generateQRImage($payload, $student, $options);

            // Update student record
            $student->update(['qr_code_path' => $filename]);

            Log::info('QR code generated successfully', [
                'student_id' => $student->student_id,
                'filename' => $filename
            ]);

            return $filename;

        } catch (Exception $e) {
            Log::error('Failed to generate QR code for student', [
                'student_id' => $student->student_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    public function generateForStudent(Student $student): string
    {
        return $this->generateStudentQR($student);
    }

    /**
     * Decrypt and validate scanned QR code.
     *
     * @param string $qrData Raw QR code data
     * @return array Validation result with student data
     */
    public function validateQRCode(string $qrData): array
    {
        try {
            Log::info('Validating QR code', ['data_length' => strlen($qrData)]);

            // Decrypt the payload
            $decryptedData = $this->decryptPayload($qrData);

            if (!$decryptedData) {
                return $this->validationError('Failed to decrypt QR code data');
            }

            // Validate structure
            if (!$this->validatePayloadStructure($decryptedData)) {
                return $this->validationError('Invalid QR code structure');
            }

            // Check expiration
            if ($this->isExpired($decryptedData['timestamp'])) {
                return $this->validationError('QR code has expired');
            }

            // Verify student exists and data integrity
            $student = Student::where('student_id', $decryptedData['student_id'])->first();

            if (!$student) {
                return $this->validationError('Student not found');
            }

            // Verify tamper-proof hash
            if (!$this->verifyIntegrity($decryptedData, $student)) {
                return $this->validationError('QR code integrity check failed');
            }

            Log::info('QR code validated successfully', ['student_id' => $student->student_id]);

            return [
                'valid' => true,
                'student' => $student,
                'data' => $decryptedData,
                'scanned_at' => now(),
            ];

        } catch (Exception $e) {
            Log::error('QR code validation failed', ['error' => $e->getMessage()]);
            return $this->validationError('QR code validation failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate QR codes for multiple students with batch processing.
     *
     * @param array $studentIds Array of student IDs
     * @param array $options Additional options
     * @return array Batch processing results
     */
    public function batchGenerateQR(array $studentIds, array $options = []): array
    {
        try {
            Log::info('Starting batch QR generation', ['count' => count($studentIds)]);

            // Validate batch size
            $maxBatchSize = $this->getMaxBatchSize();
            if (count($studentIds) > $maxBatchSize) {
                throw new Exception('Batch size exceeds maximum limit of ' . $maxBatchSize);
            }

            $students = Student::whereIn('id', $studentIds)->get();
            $results = [];
            $successCount = 0;
            $errorCount = 0;

            foreach ($students as $student) {
                try {
                    $filename = $this->generateStudentQR($student, $options);
                    $results[] = [
                        'student_id' => $student->student_id,
                        'name' => $student->full_name,
                        'qr_code_path' => $filename,
                        'status' => 'success',
                    ];
                    $successCount++;
                } catch (Exception $e) {
                    $results[] = [
                        'student_id' => $student->student_id,
                        'name' => $student->full_name,
                        'qr_code_path' => null,
                        'status' => 'error',
                        'error' => $e->getMessage(),
                    ];
                    $errorCount++;
                }
            }

            Log::info('Batch QR generation completed', [
                'total' => count($students),
                'success' => $successCount,
                'errors' => $errorCount
            ]);

            return [
                'results' => $results,
                'summary' => [
                    'total' => count($students),
                    'success' => $successCount,
                    'errors' => $errorCount,
                ]
            ];

        } catch (Exception $e) {
            Log::error('Batch QR generation failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    public function generateBatch(array $studentIds): array
    {
        $result = $this->batchGenerateQR($studentIds);
        return $result['results'];
    }

    /**
     * Create new QR code for existing student.
     *
     * @param Student $student
     * @param array $options Additional options
     * @return string Path to new QR code file
     */
    public function regenerateQR(Student $student, array $options = []): string
    {
        try {
            Log::info('Regenerating QR code for student', ['student_id' => $student->student_id]);

            // Delete old QR code if exists
            if ($student->qr_code_path && Storage::disk('public')->exists($student->qr_code_path)) {
                Storage::disk('public')->delete($student->qr_code_path);
            }

            // Clear any cached QR data
            Cache::forget("qr_data_{$student->student_id}");

            // Generate new QR code hash
            $student->generateQRCode();
            $student->save();

            // Generate new QR code
            $filename = $this->generateStudentQR($student, $options);

            Log::info('QR code regenerated successfully', [
                'student_id' => $student->student_id,
                'new_filename' => $filename
            ]);

            return $filename;

        } catch (Exception $e) {
            Log::error('Failed to regenerate QR code', [
                'student_id' => $student->student_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Extract student data from QR code.
     *
     * @param string $qrData Raw QR code data
     * @return array|null Student data or null if invalid
     */
    public function getQRData(string $qrData): ?array
    {
        try {
            $validation = $this->validateQRCode($qrData);

            if (!$validation['valid']) {
                return null;
            }

            return [
                'student_id' => $validation['data']['student_id'],
                'name' => $validation['data']['name'],
                'grade_level' => $validation['data']['grade_level'],
                'section' => $validation['data']['section'],
                'generated_at' => $validation['data']['timestamp'],
                'expires_at' => Carbon::parse($validation['data']['timestamp'])
                    ->addMinutes($this->getExpirationMinutes()),
                'student' => $validation['student'],
            ];

        } catch (Exception $e) {
            Log::error('Failed to extract QR data', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Generate QR codes for all students in a grade/section.
     */
    public function generateForGradeSection(?string $gradeLevel = null, ?string $section = null): array
    {
        $query = Student::active();

        if ($gradeLevel) {
            $query->byGradeLevel($gradeLevel);
        }

        if ($section) {
            $query->bySection($section);
        }

        $students = $query->get();
        $studentIds = $students->pluck('id')->toArray();

        return $this->batchGenerateQR($studentIds);
    }

    /**
     * Legacy method for backward compatibility
     */
    public function regenerateForStudent(Student $student): string
    {
        return $this->regenerateQR($student);
    }

    /**
     * Generate PDF with QR codes for printing.
     *
     * @param array $studentIds Array of student IDs
     * @param array $options PDF generation options
     * @return string Path to generated PDF file
     */
    public function generateQRCodesPDF(array $studentIds, array $options = []): string
    {
        try {
            Log::info('Generating QR codes PDF', ['student_count' => count($studentIds)]);

            $students = Student::whereIn('id', $studentIds)->get();
            $qrCodes = [];

            // Generate QR codes for all students
            foreach ($students as $student) {
                $qrPath = $this->generateStudentQR($student, $options);
                $qrCodes[] = [
                    'student' => $student,
                    'qr_path' => Storage::disk('public')->path($qrPath),
                    'qr_url' => Storage::disk('public')->url($qrPath),
                ];
            }

            // Generate PDF
            $pdf = Pdf::loadView('qr-codes.pdf', [
                'qr_codes' => $qrCodes,
                'generated_at' => now(),
                'options' => $options,
            ]);

            $filename = 'qr-codes/batch-' . now()->format('Y-m-d-H-i-s') . '.pdf';
            Storage::disk('public')->put($filename, $pdf->output());

            Log::info('QR codes PDF generated successfully', ['filename' => $filename]);

            return $filename;

        } catch (Exception $e) {
            Log::error('Failed to generate QR codes PDF', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    public function validateQRData(string $qrData): array
    {
        $result = $this->validateQRCode($qrData);

        if ($result['valid']) {
            return [
                'valid' => true,
                'student' => $result['student'],
                'data' => $result['data'],
            ];
        }

        return [
            'valid' => false,
            'error' => $result['error'] ?? 'Validation failed',
        ];
    }

    // ========================================
    // PRIVATE HELPER METHODS
    // ========================================

    /**
     * Create secure encrypted payload for QR code.
     */
    private function createSecurePayload(Student $student): string
    {
        $data = [
            'student_id' => $student->student_id,
            'name' => $student->full_name,
            'grade_level' => $student->grade_level,
            'section' => $student->section,
            'timestamp' => now()->toISOString(),
            'nonce' => Str::random(16),
            'hash' => $student->qr_code_hash,
        ];

        // Add integrity hash
        $data['integrity'] = $this->generateIntegrityHash($data, $student);

        // Encrypt the payload
        return Crypt::encryptString(json_encode($data));
    }

    /**
     * Decrypt QR code payload.
     */
    private function decryptPayload(string $encryptedData): ?array
    {
        try {
            $decrypted = Crypt::decryptString($encryptedData);
            return json_decode($decrypted, true);
        } catch (Exception $e) {
            Log::warning('Failed to decrypt QR payload', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Validate payload structure.
     */
    private function validatePayloadStructure(array $data): bool
    {
        $requiredFields = ['student_id', 'name', 'grade_level', 'section', 'timestamp', 'nonce', 'hash', 'integrity'];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if QR code has expired.
     */
    private function isExpired(string $timestamp): bool
    {
        $generatedAt = Carbon::parse($timestamp);
        $expiresAt = $generatedAt->addMinutes($this->getExpirationMinutes());

        return now()->isAfter($expiresAt);
    }

    /**
     * Verify QR code integrity.
     */
    private function verifyIntegrity(array $data, Student $student): bool
    {
        $expectedHash = $this->generateIntegrityHash($data, $student);
        return hash_equals($expectedHash, $data['integrity']);
    }

    /**
     * Generate integrity hash for tamper-proof design.
     */
    private function generateIntegrityHash(array $data, Student $student): string
    {
        // Remove integrity field if present to avoid circular reference
        $dataForHash = $data;
        unset($dataForHash['integrity']);

        $hashData = implode('|', [
            $dataForHash['student_id'],
            $dataForHash['name'],
            $dataForHash['grade_level'],
            $dataForHash['section'],
            $dataForHash['timestamp'],
            $dataForHash['nonce'],
            $student->qr_code_hash,
            config('app.key'), // Add app key for additional security
        ]);

        return hash_hmac('sha256', $hashData, config('app.key'));
    }

    /**
     * Generate QR code image from payload.
     */
    private function generateQRImage(string $payload, Student $student, array $options = []): string
    {
        $size = $options['size'] ?? config('qrcode.defaults.size', 300);
        $margin = $options['margin'] ?? config('qrcode.defaults.margin', 10);
        $showLabel = $options['show_label'] ?? config('qrcode.defaults.show_label', true);

        $builderOptions = [
            'writer' => new PngWriter(),
            'writerOptions' => [],
            'validateResult' => false,
            'data' => $payload,
            'encoding' => new Encoding('UTF-8'),
            'errorCorrectionLevel' => ErrorCorrectionLevel::High,
            'size' => $size,
            'margin' => $margin,
            'roundBlockSizeMode' => RoundBlockSizeMode::Margin,
        ];

        // Add label if requested
        if ($showLabel) {
            $builderOptions['labelText'] = $student->full_name;
            $builderOptions['labelAlignment'] = LabelAlignment::Center;
        }

        // Add logo if provided
        if (isset($options['logo_path']) && file_exists($options['logo_path'])) {
            $builderOptions['logoPath'] = $options['logo_path'];
            $builderOptions['logoResizeToWidth'] = 50;
            $builderOptions['logoPunchoutBackground'] = true;
        }

        $builder = new Builder(...$builderOptions);
        $result = $builder->build();

        $storagePath = config('qrcode.storage.path', 'qr-codes');
        $storageDisk = config('qrcode.storage.disk', 'public');

        $filename = $storagePath . '/student-' . $student->student_id . '-' . now()->timestamp . '-' . Str::random(8) . '.png';
        Storage::disk($storageDisk)->put($filename, $result->getString());

        return $filename;
    }

    /**
     * Create validation error response.
     */
    private function validationError(string $message): array
    {
        return [
            'valid' => false,
            'error' => $message,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get QR code statistics.
     *
     * @return array Statistics about QR codes
     */
    public function getQRCodeStats(): array
    {
        try {
            $totalStudents = Student::count();
            $studentsWithQR = Student::whereNotNull('qr_code_path')->count();
            $recentlyGenerated = Student::whereNotNull('qr_code_path')
                ->where('updated_at', '>=', now()->subDays(7))
                ->count();

            return [
                'total_students' => $totalStudents,
                'students_with_qr' => $studentsWithQR,
                'coverage_percentage' => $totalStudents > 0 ? round(($studentsWithQR / $totalStudents) * 100, 2) : 0,
                'recently_generated' => $recentlyGenerated,
                'qr_expiration_minutes' => $this->getExpirationMinutes(),
                'max_batch_size' => $this->getMaxBatchSize(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to get QR code statistics', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Clean up expired QR code files.
     *
     * @param int $daysOld Number of days old to consider for cleanup
     * @return array Cleanup results
     */
    public function cleanupExpiredQRCodes(int $daysOld = 30): array
    {
        try {
            Log::info('Starting QR code cleanup', ['days_old' => $daysOld]);

            $cutoffDate = now()->subDays($daysOld);
            $files = Storage::disk('public')->files('qr-codes');
            $deletedCount = 0;
            $errorCount = 0;

            foreach ($files as $file) {
                try {
                    $lastModified = Carbon::createFromTimestamp(Storage::disk('public')->lastModified($file));

                    if ($lastModified->isBefore($cutoffDate)) {
                        Storage::disk('public')->delete($file);
                        $deletedCount++;
                    }
                } catch (Exception $e) {
                    $errorCount++;
                    Log::warning('Failed to delete QR code file', [
                        'file' => $file,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $result = [
                'deleted_count' => $deletedCount,
                'error_count' => $errorCount,
                'cutoff_date' => $cutoffDate->toISOString(),
            ];

            Log::info('QR code cleanup completed', $result);

            return $result;

        } catch (Exception $e) {
            Log::error('QR code cleanup failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
