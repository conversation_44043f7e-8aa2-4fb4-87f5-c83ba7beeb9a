<?php

namespace Tests\Unit\Models;

use App\Enums\SmsStatus;
use App\Enums\SmsType;
use App\Models\SmsLog;
use App\Models\Teacher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SmsLogTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_sms_log()
    {
        $teacher = Teacher::factory()->create();
        
        $smsLog = SmsLog::create([
            'teacher_id' => $teacher->id,
            'recipient_phone' => '+639123456789',
            'recipient_name' => 'Test Parent',
            'message_content' => 'Test message',
            'message_type' => SmsType::MANUAL,
            'status' => SmsStatus::PENDING,
            'provider' => 'textbee',
        ]);

        $this->assertInstanceOf(SmsLog::class, $smsLog);
        $this->assertEquals($teacher->id, $smsLog->teacher_id);
        $this->assertEquals('+639123456789', $smsLog->recipient_phone);
        $this->assertEquals(SmsType::MANUAL, $smsLog->message_type);
        $this->assertEquals(SmsStatus::PENDING, $smsLog->status);
    }

    /** @test */
    public function it_belongs_to_teacher()
    {
        $teacher = Teacher::factory()->create();
        $smsLog = SmsLog::factory()->create(['teacher_id' => $teacher->id]);

        $this->assertInstanceOf(Teacher::class, $smsLog->teacher);
        $this->assertEquals($teacher->id, $smsLog->teacher->id);
    }

    /** @test */
    public function it_can_mark_as_sent()
    {
        $smsLog = SmsLog::factory()->create(['status' => SmsStatus::PENDING]);

        $smsLog->markAsSent('msg_123', ['response' => 'success']);

        $this->assertEquals(SmsStatus::SENT, $smsLog->status);
        $this->assertEquals('msg_123', $smsLog->message_id);
        $this->assertNotNull($smsLog->sent_at);
        $this->assertEquals(['response' => 'success'], $smsLog->provider_response);
    }

    /** @test */
    public function it_can_mark_as_delivered()
    {
        $smsLog = SmsLog::factory()->create(['status' => SmsStatus::SENT]);

        $smsLog->markAsDelivered(['delivered_at' => now()->toISOString()]);

        $this->assertEquals(SmsStatus::DELIVERED, $smsLog->status);
        $this->assertNotNull($smsLog->delivered_at);
    }

    /** @test */
    public function it_can_mark_as_failed()
    {
        $smsLog = SmsLog::factory()->create(['status' => SmsStatus::PENDING]);

        $smsLog->markAsFailed('Network error', ['error_code' => 500]);

        $this->assertEquals(SmsStatus::FAILED, $smsLog->status);
        $this->assertEquals('Network error', $smsLog->failure_reason);
        $this->assertNotNull($smsLog->failed_at);
        $this->assertEquals(['error_code' => 500], $smsLog->provider_response);
    }

    /** @test */
    public function it_can_increment_retry_count()
    {
        $smsLog = SmsLog::factory()->create(['retry_count' => 0]);

        $smsLog->incrementRetry();

        $this->assertEquals(1, $smsLog->retry_count);
        $this->assertNotNull($smsLog->last_retry_at);
    }

    /** @test */
    public function it_can_scope_by_teacher()
    {
        $teacher1 = Teacher::factory()->create();
        $teacher2 = Teacher::factory()->create();
        
        SmsLog::factory()->count(3)->create(['teacher_id' => $teacher1->id]);
        SmsLog::factory()->count(2)->create(['teacher_id' => $teacher2->id]);

        $teacher1Logs = SmsLog::forTeacher($teacher1->id)->get();
        $teacher2Logs = SmsLog::forTeacher($teacher2->id)->get();

        $this->assertCount(3, $teacher1Logs);
        $this->assertCount(2, $teacher2Logs);
    }

    /** @test */
    public function it_can_scope_by_status()
    {
        SmsLog::factory()->count(2)->create(['status' => SmsStatus::SENT]);
        SmsLog::factory()->count(3)->create(['status' => SmsStatus::FAILED]);

        $sentLogs = SmsLog::withStatus(SmsStatus::SENT)->get();
        $failedLogs = SmsLog::withStatus(SmsStatus::FAILED)->get();

        $this->assertCount(2, $sentLogs);
        $this->assertCount(3, $failedLogs);
    }

    /** @test */
    public function it_can_scope_by_type()
    {
        SmsLog::factory()->count(2)->create(['message_type' => SmsType::ATTENDANCE]);
        SmsLog::factory()->count(3)->create(['message_type' => SmsType::BULK]);

        $attendanceLogs = SmsLog::ofType(SmsType::ATTENDANCE)->get();
        $bulkLogs = SmsLog::ofType(SmsType::BULK)->get();

        $this->assertCount(2, $attendanceLogs);
        $this->assertCount(3, $bulkLogs);
    }

    /** @test */
    public function it_can_scope_successful_messages()
    {
        SmsLog::factory()->create(['status' => SmsStatus::SENT]);
        SmsLog::factory()->create(['status' => SmsStatus::DELIVERED]);
        SmsLog::factory()->create(['status' => SmsStatus::FAILED]);
        SmsLog::factory()->create(['status' => SmsStatus::PENDING]);

        $successfulLogs = SmsLog::successful()->get();

        $this->assertCount(2, $successfulLogs);
    }

    /** @test */
    public function it_can_scope_failed_messages()
    {
        SmsLog::factory()->create(['status' => SmsStatus::SENT]);
        SmsLog::factory()->create(['status' => SmsStatus::FAILED]);
        SmsLog::factory()->create(['status' => SmsStatus::EXPIRED]);

        $failedLogs = SmsLog::failed()->get();

        $this->assertCount(2, $failedLogs);
    }

    /** @test */
    public function it_can_scope_retryable_messages()
    {
        SmsLog::factory()->create([
            'status' => SmsStatus::FAILED,
            'retry_count' => 2,
        ]);
        
        SmsLog::factory()->create([
            'status' => SmsStatus::FAILED,
            'retry_count' => 5, // Too many retries
        ]);
        
        SmsLog::factory()->create([
            'status' => SmsStatus::SENT, // Not failed
        ]);

        $retryableLogs = SmsLog::retryable()->get();

        $this->assertCount(1, $retryableLogs);
    }

    /** @test */
    public function it_calculates_cost_correctly()
    {
        $smsLog = SmsLog::factory()->create([
            'message_content' => str_repeat('a', 160), // 1 SMS part
            'cost_per_sms' => 1.50,
        ]);

        $this->assertEquals(1.50, $smsLog->calculated_cost);

        // Test multi-part message
        $longSmsLog = SmsLog::factory()->create([
            'message_content' => str_repeat('a', 320), // 2 SMS parts
            'cost_per_sms' => 1.50,
        ]);

        $this->assertEquals(3.00, $longSmsLog->calculated_cost);
    }

    /** @test */
    public function it_determines_if_message_is_successful()
    {
        $sentLog = SmsLog::factory()->create(['status' => SmsStatus::SENT]);
        $deliveredLog = SmsLog::factory()->create(['status' => SmsStatus::DELIVERED]);
        $failedLog = SmsLog::factory()->create(['status' => SmsStatus::FAILED]);

        $this->assertTrue($sentLog->is_successful);
        $this->assertTrue($deliveredLog->is_successful);
        $this->assertFalse($failedLog->is_successful);
    }

    /** @test */
    public function it_determines_if_message_is_failed()
    {
        $failedLog = SmsLog::factory()->create(['status' => SmsStatus::FAILED]);
        $expiredLog = SmsLog::factory()->create(['status' => SmsStatus::EXPIRED]);
        $sentLog = SmsLog::factory()->create(['status' => SmsStatus::SENT]);

        $this->assertTrue($failedLog->is_failed);
        $this->assertTrue($expiredLog->is_failed);
        $this->assertFalse($sentLog->is_failed);
    }
}
