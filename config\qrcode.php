<?php

return [

    /*
    |--------------------------------------------------------------------------
    | QR Code Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the QR Code service.
    | You can customize various aspects of QR code generation and validation.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Expiration Settings
    |--------------------------------------------------------------------------
    |
    | Configure how long QR codes remain valid before expiring.
    |
    */
    'expiration_minutes' => env('QR_EXPIRATION_MINUTES', 60),

    /*
    |--------------------------------------------------------------------------
    | Batch Processing
    |--------------------------------------------------------------------------
    |
    | Configure limits for batch QR code generation.
    |
    */
    'max_batch_size' => env('QR_MAX_BATCH_SIZE', 100),

    /*
    |--------------------------------------------------------------------------
    | QR Code Appearance
    |--------------------------------------------------------------------------
    |
    | Default settings for QR code appearance and styling.
    |
    */
    'defaults' => [
        'size' => env('QR_DEFAULT_SIZE', 300),
        'margin' => env('QR_DEFAULT_MARGIN', 10),
        'show_label' => env('QR_SHOW_LABEL', true),
        'error_correction' => 'high', // low, medium, quartile, high
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Settings
    |--------------------------------------------------------------------------
    |
    | Configure where QR codes are stored and how they're organized.
    |
    */
    'storage' => [
        'disk' => env('QR_STORAGE_DISK', 'public'),
        'path' => env('QR_STORAGE_PATH', 'qr-codes'),
        'cleanup_days' => env('QR_CLEANUP_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security features for QR code generation and validation.
    |
    */
    'security' => [
        'encryption_enabled' => env('QR_ENCRYPTION_ENABLED', true),
        'integrity_check' => env('QR_INTEGRITY_CHECK', true),
        'time_validation' => env('QR_TIME_VALIDATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | PDF Generation
    |--------------------------------------------------------------------------
    |
    | Settings for generating PDF files with QR codes.
    |
    */
    'pdf' => [
        'paper_size' => env('QR_PDF_PAPER_SIZE', 'a4'),
        'orientation' => env('QR_PDF_ORIENTATION', 'portrait'),
        'margin' => env('QR_PDF_MARGIN', 15),
        'qr_per_page' => env('QR_PDF_QR_PER_PAGE', 12),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Configure logging for QR code operations.
    |
    */
    'logging' => [
        'enabled' => env('QR_LOGGING_ENABLED', true),
        'level' => env('QR_LOG_LEVEL', 'info'),
        'channel' => env('QR_LOG_CHANNEL', 'single'),
    ],

];
