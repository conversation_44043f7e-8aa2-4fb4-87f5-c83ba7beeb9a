<?php

namespace App\Services;

use App\Models\Attendance;
use App\Models\Student;
use App\Models\Subject;
use App\Enums\AttendanceStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class AttendanceReportService
{
    /**
     * Generate attendance report based on parameters.
     */
    public function generateReport(array $parameters): array
    {
        try {
            $reportType = $parameters['report_type'];
            $format = $parameters['format'] ?? 'pdf';
            
            // Get attendance data
            $attendanceData = $this->getAttendanceData($parameters);
            
            // Generate report based on type
            $reportContent = match($reportType) {
                'sf2' => $this->generateSF2Report($attendanceData, $parameters),
                'sf4' => $this->generateSF4Report($attendanceData, $parameters),
                'daily' => $this->generateDailyReport($attendanceData, $parameters),
                'weekly' => $this->generateWeeklyReport($attendanceData, $parameters),
                'monthly' => $this->generateMonthlyReport($attendanceData, $parameters),
                default => $this->generateDailyReport($attendanceData, $parameters),
            };
            
            // Generate file based on format
            $filePath = $this->generateReportFile($reportContent, $format, $reportType, $parameters);
            
            return [
                'path' => $filePath,
                'type' => $reportType,
                'format' => $format,
                'generated_at' => now(),
            ];
            
        } catch (\Exception $e) {
            Log::error('Failed to generate attendance report', [
                'parameters' => $parameters,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Get attendance data based on parameters.
     */
    protected function getAttendanceData(array $parameters): array
    {
        $query = Attendance::with(['student', 'teacher', 'subject'])
            ->byDateRange($parameters['date_from'], $parameters['date_to']);

        // Apply filters
        if (!empty($parameters['grade_level']) || !empty($parameters['section'])) {
            $query->whereHas('student', function ($q) use ($parameters) {
                if (!empty($parameters['grade_level'])) {
                    $q->byGradeLevel($parameters['grade_level']);
                }
                if (!empty($parameters['section'])) {
                    $q->bySection($parameters['section']);
                }
            });
        }

        if (!empty($parameters['subject_id'])) {
            $query->where('subject_id', $parameters['subject_id']);
        }

        $attendanceRecords = $query->orderBy('date')
            ->orderBy('student_id')
            ->get();

        // Get students list
        $studentsQuery = Student::query();
        
        if (!empty($parameters['grade_level'])) {
            $studentsQuery->byGradeLevel($parameters['grade_level']);
        }
        
        if (!empty($parameters['section'])) {
            $studentsQuery->bySection($parameters['section']);
        }
        
        $students = $studentsQuery->active()
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        return [
            'attendance_records' => $attendanceRecords,
            'students' => $students,
            'date_from' => Carbon::parse($parameters['date_from']),
            'date_to' => Carbon::parse($parameters['date_to']),
            'subject' => !empty($parameters['subject_id']) ? Subject::find($parameters['subject_id']) : null,
            'grade_level' => $parameters['grade_level'] ?? null,
            'section' => $parameters['section'] ?? null,
        ];
    }

    /**
     * Generate SF2 (School Form 2) report.
     */
    protected function generateSF2Report(array $data, array $parameters): array
    {
        $students = $data['students'];
        $attendanceRecords = $data['attendance_records']->groupBy('student_id');
        $dateFrom = $data['date_from'];
        $dateTo = $data['date_to'];
        
        // Create date range
        $dateRange = [];
        $currentDate = $dateFrom->copy();
        while ($currentDate->lte($dateTo)) {
            $dateRange[] = $currentDate->copy();
            $currentDate->addDay();
        }

        $reportData = [
            'title' => 'School Form 2 - Daily Attendance Record',
            'school_name' => config('app.name', 'School Name'),
            'grade_level' => $data['grade_level'],
            'section' => $data['section'],
            'subject' => $data['subject']?->name,
            'date_from' => $dateFrom->format('M d, Y'),
            'date_to' => $dateTo->format('M d, Y'),
            'date_range' => $dateRange,
            'students_data' => [],
        ];

        foreach ($students as $student) {
            $studentAttendance = $attendanceRecords->get($student->id, collect());
            $dailyAttendance = [];
            
            foreach ($dateRange as $date) {
                $attendance = $studentAttendance->firstWhere('date', $date->format('Y-m-d'));
                $dailyAttendance[] = [
                    'date' => $date,
                    'status' => $attendance?->status ?? null,
                    'time_in' => $attendance?->time_in,
                    'time_out' => $attendance?->time_out,
                ];
            }
            
            $reportData['students_data'][] = [
                'student' => $student,
                'daily_attendance' => $dailyAttendance,
                'summary' => $this->calculateStudentSummary($studentAttendance),
            ];
        }

        return $reportData;
    }

    /**
     * Generate SF4 (School Form 4) report.
     */
    protected function generateSF4Report(array $data, array $parameters): array
    {
        $students = $data['students'];
        $attendanceRecords = $data['attendance_records']->groupBy('student_id');
        
        $reportData = [
            'title' => 'School Form 4 - Monthly Attendance Summary',
            'school_name' => config('app.name', 'School Name'),
            'grade_level' => $data['grade_level'],
            'section' => $data['section'],
            'subject' => $data['subject']?->name,
            'month_year' => $data['date_from']->format('F Y'),
            'students_summary' => [],
            'class_summary' => [],
        ];

        $classTotals = [
            'total_students' => $students->count(),
            'total_present' => 0,
            'total_absent' => 0,
            'total_late' => 0,
            'total_excused' => 0,
        ];

        foreach ($students as $student) {
            $studentAttendance = $attendanceRecords->get($student->id, collect());
            $summary = $this->calculateStudentSummary($studentAttendance);
            
            $reportData['students_summary'][] = [
                'student' => $student,
                'summary' => $summary,
            ];
            
            // Add to class totals
            $classTotals['total_present'] += $summary['present_count'];
            $classTotals['total_absent'] += $summary['absent_count'];
            $classTotals['total_late'] += $summary['late_count'];
            $classTotals['total_excused'] += $summary['excused_count'];
        }

        $reportData['class_summary'] = $classTotals;

        return $reportData;
    }

    /**
     * Generate daily attendance report.
     */
    protected function generateDailyReport(array $data, array $parameters): array
    {
        return [
            'title' => 'Daily Attendance Report',
            'school_name' => config('app.name', 'School Name'),
            'date' => $data['date_from']->format('F d, Y'),
            'grade_level' => $data['grade_level'],
            'section' => $data['section'],
            'subject' => $data['subject']?->name,
            'attendance_data' => $data['attendance_records'],
            'summary' => $this->calculateDailySummary($data['attendance_records']),
        ];
    }

    /**
     * Generate weekly attendance report.
     */
    protected function generateWeeklyReport(array $data, array $parameters): array
    {
        $weeklyData = $data['attendance_records']->groupBy(function ($record) {
            return $record->date->format('Y-W');
        });

        return [
            'title' => 'Weekly Attendance Report',
            'school_name' => config('app.name', 'School Name'),
            'date_from' => $data['date_from']->format('M d, Y'),
            'date_to' => $data['date_to']->format('M d, Y'),
            'grade_level' => $data['grade_level'],
            'section' => $data['section'],
            'subject' => $data['subject']?->name,
            'weekly_data' => $weeklyData,
            'summary' => $this->calculateWeeklySummary($weeklyData),
        ];
    }

    /**
     * Generate monthly attendance report.
     */
    protected function generateMonthlyReport(array $data, array $parameters): array
    {
        $monthlyData = $data['attendance_records']->groupBy(function ($record) {
            return $record->date->format('Y-m');
        });

        return [
            'title' => 'Monthly Attendance Report',
            'school_name' => config('app.name', 'School Name'),
            'date_from' => $data['date_from']->format('M d, Y'),
            'date_to' => $data['date_to']->format('M d, Y'),
            'grade_level' => $data['grade_level'],
            'section' => $data['section'],
            'subject' => $data['subject']?->name,
            'monthly_data' => $monthlyData,
            'summary' => $this->calculateMonthlySummary($monthlyData),
        ];
    }

    /**
     * Generate report file based on format.
     */
    protected function generateReportFile(array $reportContent, string $format, string $reportType, array $parameters): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "{$reportType}_report_{$timestamp}";
        
        switch ($format) {
            case 'pdf':
                return $this->generatePDFFile($reportContent, $filename);
            case 'excel':
                return $this->generateExcelFile($reportContent, $filename);
            case 'csv':
                return $this->generateCSVFile($reportContent, $filename);
            default:
                throw new \InvalidArgumentException("Unsupported format: {$format}");
        }
    }

    /**
     * Generate PDF file.
     */
    protected function generatePDFFile(array $reportContent, string $filename): string
    {
        $pdf = Pdf::loadView('reports.attendance.pdf', $reportContent);
        $filePath = "reports/attendance/{$filename}.pdf";
        
        Storage::put($filePath, $pdf->output());
        
        return $filePath;
    }

    /**
     * Generate Excel file.
     */
    protected function generateExcelFile(array $reportContent, string $filename): string
    {
        // This would use Laravel Excel package
        // For now, return a placeholder
        $filePath = "reports/attendance/{$filename}.xlsx";
        
        // Placeholder implementation
        Storage::put($filePath, json_encode($reportContent));
        
        return $filePath;
    }

    /**
     * Generate CSV file.
     */
    protected function generateCSVFile(array $reportContent, string $filename): string
    {
        $filePath = "reports/attendance/{$filename}.csv";
        
        // Convert report content to CSV format
        $csvContent = $this->convertToCSV($reportContent);
        Storage::put($filePath, $csvContent);
        
        return $filePath;
    }

    /**
     * Calculate student attendance summary.
     */
    protected function calculateStudentSummary($attendanceRecords): array
    {
        return [
            'total_days' => $attendanceRecords->count(),
            'present_count' => $attendanceRecords->where('status', AttendanceStatus::PRESENT)->count(),
            'absent_count' => $attendanceRecords->where('status', AttendanceStatus::ABSENT)->count(),
            'late_count' => $attendanceRecords->where('status', AttendanceStatus::LATE)->count(),
            'excused_count' => $attendanceRecords->where('status', AttendanceStatus::EXCUSED)->count(),
        ];
    }

    /**
     * Calculate daily summary.
     */
    protected function calculateDailySummary($attendanceRecords): array
    {
        return [
            'total_records' => $attendanceRecords->count(),
            'present_count' => $attendanceRecords->where('status', AttendanceStatus::PRESENT)->count(),
            'absent_count' => $attendanceRecords->where('status', AttendanceStatus::ABSENT)->count(),
            'late_count' => $attendanceRecords->where('status', AttendanceStatus::LATE)->count(),
            'excused_count' => $attendanceRecords->where('status', AttendanceStatus::EXCUSED)->count(),
        ];
    }

    /**
     * Calculate weekly summary.
     */
    protected function calculateWeeklySummary($weeklyData): array
    {
        $summary = [];
        
        foreach ($weeklyData as $week => $records) {
            $summary[$week] = $this->calculateDailySummary($records);
        }
        
        return $summary;
    }

    /**
     * Calculate monthly summary.
     */
    protected function calculateMonthlySummary($monthlyData): array
    {
        $summary = [];
        
        foreach ($monthlyData as $month => $records) {
            $summary[$month] = $this->calculateDailySummary($records);
        }
        
        return $summary;
    }

    /**
     * Convert report content to CSV format.
     */
    protected function convertToCSV(array $reportContent): string
    {
        // Basic CSV conversion - can be enhanced based on report type
        $csv = '';
        
        // Add headers
        $csv .= "Report: " . ($reportContent['title'] ?? 'Attendance Report') . "\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        // Add basic data structure
        if (isset($reportContent['attendance_data'])) {
            $csv .= "Student ID,Student Name,Date,Status,Time In,Time Out\n";
            
            foreach ($reportContent['attendance_data'] as $record) {
                $csv .= implode(',', [
                    $record->student->student_id ?? '',
                    '"' . ($record->student->full_name ?? '') . '"',
                    $record->date ?? '',
                    $record->status->value ?? '',
                    $record->time_in ?? '',
                    $record->time_out ?? '',
                ]) . "\n";
            }
        }
        
        return $csv;
    }
}
