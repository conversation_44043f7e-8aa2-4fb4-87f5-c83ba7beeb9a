<?php

namespace App\Models;

use App\Enums\QueueStatus;
use App\Enums\SmsProvider;
use App\Enums\SmsType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class SmsQueue extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'sms_queue';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'batch_id',
        'priority',
        'teacher_id',
        'recipient_phone',
        'recipient_name',
        'recipient_type',
        'message_content',
        'message_type',
        'template_used',
        'status',
        'scheduled_at',
        'processed_at',
        'provider',
        'provider_options',
        'max_retries',
        'retry_delay',
        'error_message',
        'error_details',
        'metadata',
        'ip_address',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'priority' => 'integer',
            'message_type' => SmsType::class,
            'status' => QueueStatus::class,
            'provider' => SmsProvider::class,
            'provider_options' => 'array',
            'max_retries' => 'integer',
            'retry_delay' => 'integer',
            'error_details' => 'array',
            'metadata' => 'array',
            'scheduled_at' => 'datetime',
            'processed_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // RELATIONSHIPS

    /**
     * Get the teacher who queued the message.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    // SCOPES

    /**
     * Scope for pending messages.
     */
    public function scopePending(Builder $query): void
    {
        $query->where('status', QueueStatus::PENDING);
    }

    /**
     * Scope for processing messages.
     */
    public function scopeProcessing(Builder $query): void
    {
        $query->where('status', QueueStatus::PROCESSING);
    }

    /**
     * Scope for failed messages.
     */
    public function scopeFailed(Builder $query): void
    {
        $query->where('status', QueueStatus::FAILED);
    }

    /**
     * Scope for ready to process messages.
     */
    public function scopeReadyToProcess(Builder $query): void
    {
        $query->where('status', QueueStatus::PENDING)
              ->where(function ($q) {
                  $q->whereNull('scheduled_at')
                    ->orWhere('scheduled_at', '<=', now());
              })
              ->orderBy('priority')
              ->orderBy('created_at');
    }

    /**
     * Scope for batch messages.
     */
    public function scopeInBatch(Builder $query, string $batchId): void
    {
        $query->where('batch_id', $batchId);
    }

    /**
     * Scope for teacher messages.
     */
    public function scopeForTeacher(Builder $query, int $teacherId): void
    {
        $query->where('teacher_id', $teacherId);
    }

    /**
     * Scope for scheduled messages.
     */
    public function scopeScheduled(Builder $query): void
    {
        $query->whereNotNull('scheduled_at')
              ->where('scheduled_at', '>', now());
    }

    // ACCESSORS & MUTATORS

    /**
     * Check if message is ready to process.
     */
    public function getIsReadyAttribute(): bool
    {
        return $this->status === QueueStatus::PENDING &&
               ($this->scheduled_at === null || $this->scheduled_at <= now());
    }

    /**
     * Check if message can be cancelled.
     */
    public function getCanCancelAttribute(): bool
    {
        return $this->status->canCancel();
    }

    /**
     * Get formatted scheduled time.
     */
    public function getFormattedScheduledAtAttribute(): ?string
    {
        return $this->scheduled_at?->format('M d, Y h:i A');
    }

    // METHODS

    /**
     * Mark as processing.
     */
    public function markAsProcessing(): void
    {
        $this->update([
            'status' => QueueStatus::PROCESSING,
        ]);
    }

    /**
     * Mark as processed.
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'status' => QueueStatus::PROCESSED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark as failed.
     */
    public function markAsFailed(string $error, array $details = []): void
    {
        $this->update([
            'status' => QueueStatus::FAILED,
            'error_message' => $error,
            'error_details' => $details,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark as cancelled.
     */
    public function markAsCancelled(): void
    {
        $this->update([
            'status' => QueueStatus::CANCELLED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Reset to pending status for retry.
     */
    public function resetToPending(): void
    {
        $this->update([
            'status' => QueueStatus::PENDING,
            'error_message' => null,
            'error_details' => null,
            'processed_at' => null,
        ]);
    }

    /**
     * Schedule message for later processing.
     */
    public function scheduleFor(\DateTime $dateTime): void
    {
        $this->update([
            'scheduled_at' => $dateTime,
        ]);
    }

    /**
     * Get character count.
     */
    public function getCharacterCount(): int
    {
        return strlen($this->message_content);
    }

    /**
     * Get estimated message parts.
     */
    public function getEstimatedParts(): int
    {
        $length = $this->getCharacterCount();
        
        if ($length <= 160) {
            return 1;
        }
        
        return ceil($length / 153); // 153 chars per part for multi-part SMS
    }

    /**
     * Get estimated cost.
     */
    public function getEstimatedCost(): float
    {
        $parts = $this->getEstimatedParts();
        $costPerPart = $this->provider->estimatedCost();
        
        return $parts * $costPerPart;
    }
}
