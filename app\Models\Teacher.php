<?php

namespace App\Models;

use App\Enums\TeacherRole;
use App\Services\SmsService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Teacher extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'teacher_id',
        'first_name',
        'last_name',
        'phone_number',
        'email',
        'role',
        'assigned_sections',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'role' => TeacherRole::class,
            'assigned_sections' => 'array',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // Update last_login_at when teacher logs in
        static::retrieved(function (Teacher $teacher) {
            if (auth()->check() && auth()->id() === $teacher->id) {
                $teacher->updateQuietly(['last_login_at' => now()]);
            }
        });
    }

    // RELATIONSHIPS

    /**
     * Get the attendance records for the teacher.
     */
    public function attendance(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the students assigned to this teacher through sections.
     */
    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class, 'student_teacher_sections')
                    ->withPivot('subject_id', 'section')
                    ->withTimestamps();
    }

    /**
     * Get students for a specific section.
     */
    public function studentsInSection(string $section): BelongsToMany
    {
        return $this->students()->wherePivot('section', $section);
    }

    // SCOPES

    /**
     * Scope a query to only include active teachers.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by role.
     */
    public function scopeByRole(Builder $query, TeacherRole $role): void
    {
        $query->where('role', $role);
    }

    /**
     * Scope a query to filter teachers assigned to a specific section.
     */
    public function scopeAssignedToSection(Builder $query, string $section): void
    {
        $query->whereJsonContains('assigned_sections', $section);
    }

    /**
     * Scope a query to filter teachers with specific permissions.
     */
    public function scopeWithPermission(Builder $query, string $permission): void
    {
        $query->where(function ($q) use ($permission) {
            foreach (TeacherRole::cases() as $role) {
                if (in_array($permission, $role->permissions())) {
                    $q->orWhere('role', $role);
                }
            }
        });
    }

    // ACCESSORS

    /**
     * Get the teacher's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get formatted phone number with country code.
     */
    public function getFormattedPhoneAttribute(): string
    {
        $phone = preg_replace('/[^0-9]/', '', $this->phone_number);

        // Add Philippines country code if not present
        if (strlen($phone) === 10 && !str_starts_with($phone, '63')) {
            $phone = '63' . $phone;
        }

        return '+' . $phone;
    }

    /**
     * Check if teacher is online (logged in within last 15 minutes).
     */
    public function getIsOnlineAttribute(): bool
    {
        return $this->last_login_at && $this->last_login_at->gt(now()->subMinutes(15));
    }

    // CUSTOM METHODS

    /**
     * Get all students assigned to this teacher.
     */
    public function getAssignedStudents(?string $section = null): Collection
    {
        $query = $this->students();

        if ($section) {
            $query->wherePivot('section', $section);
        }

        return $query->get();
    }

    /**
     * Check if teacher can access a specific section.
     */
    public function canAccessSection(string $section): bool
    {
        // Principals and admins can access all sections
        if (in_array($this->role, [TeacherRole::PRINCIPAL, TeacherRole::ADMIN])) {
            return true;
        }

        // Check if section is in assigned_sections
        return in_array($section, $this->assigned_sections ?? []);
    }

    /**
     * Send SMS to students' parents in assigned sections.
     */
    public function sendSMS(string $message, ?string $section = null, ?array $studentIds = null): array
    {
        $smsService = app(\App\Services\TextBeeSmsService::class);
        $results = [];

        // Get target students
        $students = $this->getAssignedStudents($section);

        if ($studentIds) {
            $students = $students->whereIn('id', $studentIds);
        }

        foreach ($students as $student) {
            if (!$student->parent_phone) {
                $results[] = [
                    'student_id' => $student->id,
                    'student_name' => $student->full_name,
                    'success' => false,
                    'error' => 'No parent phone number',
                ];
                continue;
            }

            try {
                $result = $smsService->sendSMS(
                    phone: $student->parent_phone,
                    message: $message,
                    type: \App\Enums\SmsType::MANUAL,
                    teacherId: $this->id,
                    recipientName: $student->full_name,
                    recipientType: 'parent',
                    metadata: [
                        'student_id' => $student->id,
                        'section' => $section,
                    ]
                );

                $results[] = [
                    'student_id' => $student->id,
                    'student_name' => $student->full_name,
                    'phone' => $student->parent_phone,
                    'success' => $result['success'],
                    'error' => $result['success'] ? null : ($result['error'] ?? 'SMS sending failed'),
                    'message_id' => $result['message_id'] ?? null,
                    'cost' => $result['cost'] ?? null,
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'student_id' => $student->id,
                    'student_name' => $student->full_name,
                    'phone' => $student->parent_phone,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    // PERMISSION METHODS

    /**
     * Check if teacher has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->role->permissions());
    }

    /**
     * Check if teacher has any of the given permissions.
     */
    public function hasAnyPermission(array $permissions): bool
    {
        $teacherPermissions = $this->role->permissions();

        return !empty(array_intersect($permissions, $teacherPermissions));
    }

    /**
     * Check if teacher has all of the given permissions.
     */
    public function hasAllPermissions(array $permissions): bool
    {
        $teacherPermissions = $this->role->permissions();

        return empty(array_diff($permissions, $teacherPermissions));
    }

    /**
     * Check if teacher can manage another teacher.
     */
    public function canManage(Teacher $teacher): bool
    {
        return $this->role->canAccess($teacher->role);
    }

    /**
     * Get all permissions for this teacher.
     */
    public function getAllPermissions(): array
    {
        return $this->role->permissions();
    }

    // AUTHENTICATION METHODS

    /**
     * Create API token for the teacher.
     */
    public function createApiToken(string $name = 'api-token', array $abilities = ['*']): string
    {
        // Revoke existing tokens
        $this->tokens()->delete();

        // Create new token
        $token = $this->createToken($name, $abilities);

        return $token->plainTextToken;
    }

    /**
     * Revoke all API tokens.
     */
    public function revokeAllTokens(): void
    {
        $this->tokens()->delete();
    }

    // VALIDATION

    /**
     * Get validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'teacher_id' => ['required', 'string', 'max:50', 'unique:teachers,teacher_id'],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'phone_number' => ['required', 'string', 'max:20'],
            'email' => ['required', 'email', 'max:255', 'unique:teachers,email'],
            'password' => ['required', 'string', 'min:8'],
            'role' => ['required', 'in:' . implode(',', TeacherRole::values())],
            'assigned_sections' => ['nullable', 'array'],
            'assigned_sections.*' => ['string', 'max:10'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get validation rules for updating the model.
     */
    public static function updateValidationRules(int $id): array
    {
        return [
            'teacher_id' => ['required', 'string', 'max:50', 'unique:teachers,teacher_id,' . $id],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'phone_number' => ['required', 'string', 'max:20'],
            'email' => ['required', 'email', 'max:255', 'unique:teachers,email,' . $id],
            'role' => ['required', 'in:' . implode(',', TeacherRole::values())],
            'assigned_sections' => ['nullable', 'array'],
            'assigned_sections.*' => ['string', 'max:10'],
            'is_active' => ['boolean'],
        ];
    }
}
