<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class SmsRateLimit extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'sms_rate_limits';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'identifier',
        'type',
        'limit_count',
        'limit_period',
        'period_start',
        'period_end',
        'current_count',
        'last_request_at',
        'is_exceeded',
        'reset_at',
        'exceeded_count',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'limit_count' => 'integer',
            'current_count' => 'integer',
            'exceeded_count' => 'integer',
            'is_exceeded' => 'boolean',
            'period_start' => 'datetime',
            'period_end' => 'datetime',
            'last_request_at' => 'datetime',
            'reset_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // SCOPES

    /**
     * Scope for specific identifier and type.
     */
    public function scopeForIdentifier(Builder $query, string $identifier, string $type): void
    {
        $query->where('identifier', $identifier)
              ->where('type', $type);
    }

    /**
     * Scope for exceeded rate limits.
     */
    public function scopeExceeded(Builder $query): void
    {
        $query->where('is_exceeded', true);
    }

    /**
     * Scope for active rate limits.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('period_end', '>', now());
    }

    /**
     * Scope for expired rate limits.
     */
    public function scopeExpired(Builder $query): void
    {
        $query->where('period_end', '<=', now());
    }

    // METHODS

    /**
     * Check if rate limit is exceeded.
     */
    public function isExceeded(): bool
    {
        return $this->current_count >= $this->limit_count;
    }

    /**
     * Check if rate limit period is active.
     */
    public function isActive(): bool
    {
        return now()->between($this->period_start, $this->period_end);
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('current_count');
        $this->update(['last_request_at' => now()]);
        
        if ($this->isExceeded() && !$this->is_exceeded) {
            $this->update([
                'is_exceeded' => true,
                'reset_at' => $this->period_end,
            ]);
            $this->increment('exceeded_count');
        }
    }

    /**
     * Reset rate limit for new period.
     */
    public function resetForNewPeriod(): void
    {
        $periodStart = now();
        $periodEnd = $this->calculatePeriodEnd($periodStart);
        
        $this->update([
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
            'current_count' => 0,
            'is_exceeded' => false,
            'reset_at' => null,
        ]);
    }

    /**
     * Calculate period end based on period type.
     */
    private function calculatePeriodEnd(\DateTime $start): \DateTime
    {
        return match($this->limit_period) {
            'minute' => (clone $start)->addMinute(),
            'hour' => (clone $start)->addHour(),
            'day' => (clone $start)->addDay(),
            'month' => (clone $start)->addMonth(),
            default => (clone $start)->addHour(),
        };
    }

    /**
     * Get remaining requests.
     */
    public function getRemainingRequests(): int
    {
        return max(0, $this->limit_count - $this->current_count);
    }

    /**
     * Get time until reset.
     */
    public function getTimeUntilReset(): int
    {
        if (!$this->isActive()) {
            return 0;
        }
        
        return max(0, $this->period_end->diffInSeconds(now()));
    }

    /**
     * Get usage percentage.
     */
    public function getUsagePercentage(): float
    {
        if ($this->limit_count === 0) {
            return 0;
        }
        
        return min(100, ($this->current_count / $this->limit_count) * 100);
    }
}
