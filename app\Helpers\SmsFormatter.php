<?php

namespace App\Helpers;

use App\Models\SmsTemplate;
use Carbon\Carbon;

class SmsFormatter
{
    /**
     * Format SMS message using template and variables.
     */
    public static function format(string $template, array $variables = []): string
    {
        $message = $template;
        
        // Replace variables
        foreach ($variables as $key => $value) {
            $placeholder = "{{$key}}";
            $message = str_replace($placeholder, (string) $value, $message);
        }
        
        // Clean up any remaining placeholders
        $message = preg_replace('/\{[^}]+\}/', '', $message);
        
        // Clean up extra whitespace
        $message = preg_replace('/\s+/', ' ', trim($message));
        
        return $message;
    }

    /**
     * Format message using template name.
     */
    public static function formatWithTemplate(string $templateName, array $variables = []): ?string
    {
        $template = SmsTemplate::where('name', $templateName)
            ->where('is_active', true)
            ->first();

        if (!$template) {
            return null;
        }

        return self::format($template->content, $variables);
    }

    /**
     * Format attendance message.
     */
    public static function formatAttendanceMessage(
        string $studentName,
        string $parentName,
        string $status,
        string $subject,
        string $date,
        ?string $time = null,
        ?string $schoolName = null
    ): string {
        $schoolName = $schoolName ?? config('app.school_name', 'School');
        
        $templateName = match(strtolower($status)) {
            'present' => 'attendance_present',
            'absent' => 'attendance_absent',
            'late' => 'attendance_late',
            'excused' => 'attendance_excused',
            default => 'attendance_present',
        };

        $variables = [
            'student_name' => $studentName,
            'parent_name' => $parentName,
            'status' => strtoupper($status),
            'subject' => $subject,
            'date' => $date,
            'time' => $time ?? 'N/A',
            'school_name' => $schoolName,
        ];

        $message = self::formatWithTemplate($templateName, $variables);
        
        // Fallback to generic message if template not found
        if (!$message) {
            $message = "Dear {$parentName}, your child {$studentName} was marked {$status} in {$subject} on {$date}" . 
                      ($time ? " at {$time}" : '') . ". - {$schoolName}";
        }

        return $message;
    }

    /**
     * Format emergency alert message.
     */
    public static function formatEmergencyAlert(
        string $message,
        ?string $contactNumber = null,
        ?string $schoolName = null
    ): string {
        $schoolName = $schoolName ?? config('app.school_name', 'School');
        $contactNumber = $contactNumber ?? config('app.contact_number', 'school office');

        $variables = [
            'message' => $message,
            'contact_number' => $contactNumber,
            'school_name' => $schoolName,
        ];

        $formatted = self::formatWithTemplate('emergency_alert', $variables);
        
        // Fallback message
        if (!$formatted) {
            $formatted = "EMERGENCY ALERT: {$message} Please contact the school immediately at {$contactNumber}. - {$schoolName}";
        }

        return $formatted;
    }

    /**
     * Format class cancellation message.
     */
    public static function formatClassCancellation(
        string $subject,
        string $date,
        string $time,
        string $reason,
        ?string $schoolName = null
    ): string {
        $schoolName = $schoolName ?? config('app.school_name', 'School');

        $variables = [
            'subject' => $subject,
            'date' => $date,
            'time' => $time,
            'reason' => $reason,
            'school_name' => $schoolName,
        ];

        $message = self::formatWithTemplate('class_cancellation', $variables);
        
        // Fallback message
        if (!$message) {
            $message = "Dear Parents, {$subject} class on {$date} at {$time} has been CANCELLED due to {$reason}. We will notify you of the makeup schedule. - {$schoolName}";
        }

        return $message;
    }

    /**
     * Format exam reminder message.
     */
    public static function formatExamReminder(
        string $studentName,
        string $subject,
        string $date,
        string $time,
        ?string $schoolName = null
    ): string {
        $schoolName = $schoolName ?? config('app.school_name', 'School');

        $variables = [
            'student_name' => $studentName,
            'subject' => $subject,
            'date' => $date,
            'time' => $time,
            'school_name' => $schoolName,
        ];

        $message = self::formatWithTemplate('exam_reminder', $variables);
        
        // Fallback message
        if (!$message) {
            $message = "Reminder: {$studentName} has a {$subject} exam on {$date} at {$time}. Please ensure your child is prepared and arrives on time. - {$schoolName}";
        }

        return $message;
    }

    /**
     * Format custom message.
     */
    public static function formatCustomMessage(
        string $recipientName,
        string $message,
        string $senderName
    ): string {
        $variables = [
            'recipient_name' => $recipientName,
            'message' => $message,
            'sender_name' => $senderName,
        ];

        $formatted = self::formatWithTemplate('custom_message', $variables);
        
        // Fallback message
        if (!$formatted) {
            $formatted = "Dear {$recipientName}, {$message} - {$senderName}";
        }

        return $formatted;
    }

    /**
     * Truncate message to fit SMS character limits.
     */
    public static function truncate(string $message, int $maxLength = 160): string
    {
        if (strlen($message) <= $maxLength) {
            return $message;
        }

        // Try to truncate at word boundary
        $truncated = substr($message, 0, $maxLength - 3);
        $lastSpace = strrpos($truncated, ' ');
        
        if ($lastSpace !== false && $lastSpace > ($maxLength * 0.8)) {
            $truncated = substr($truncated, 0, $lastSpace);
        }
        
        return $truncated . '...';
    }

    /**
     * Calculate SMS parts based on character count.
     */
    public static function calculateParts(string $message): int
    {
        $length = strlen($message);
        
        if ($length <= 160) {
            return 1;
        }
        
        return ceil($length / 153); // 153 chars per part for multi-part SMS
    }

    /**
     * Estimate SMS cost based on parts and rate.
     */
    public static function estimateCost(string $message, float $costPerSms = 0.50): float
    {
        $parts = self::calculateParts($message);
        return $parts * $costPerSms;
    }

    /**
     * Validate message variables against template.
     */
    public static function validateVariables(string $templateName, array $variables): array
    {
        $template = SmsTemplate::where('name', $templateName)
            ->where('is_active', true)
            ->first();

        if (!$template) {
            return [
                'valid' => false,
                'errors' => ['Template not found'],
                'missing' => [],
                'extra' => array_keys($variables),
            ];
        }

        $requiredVariables = $template->variables ?? [];
        $providedVariables = array_keys($variables);
        
        $missing = array_diff($requiredVariables, $providedVariables);
        $extra = array_diff($providedVariables, $requiredVariables);
        
        $errors = [];
        if (!empty($missing)) {
            $errors[] = 'Missing required variables: ' . implode(', ', $missing);
        }
        
        return [
            'valid' => empty($missing),
            'errors' => $errors,
            'missing' => $missing,
            'extra' => $extra,
        ];
    }

    /**
     * Get sample output for template.
     */
    public static function getSampleOutput(string $templateName): ?string
    {
        $template = SmsTemplate::where('name', $templateName)
            ->where('is_active', true)
            ->first();

        if (!$template) {
            return null;
        }

        // Create sample variables
        $sampleVariables = [];
        foreach ($template->variables ?? [] as $variable) {
            $sampleVariables[$variable] = match($variable) {
                'student_name' => 'John Doe',
                'parent_name' => 'Mrs. Doe',
                'teacher_name' => 'Ms. Smith',
                'subject' => 'Mathematics',
                'date' => Carbon::now()->format('M d, Y'),
                'time' => Carbon::now()->format('h:i A'),
                'school_name' => 'Sample School',
                'status' => 'PRESENT',
                'message' => 'This is a sample message',
                'reason' => 'weather conditions',
                'contact_number' => '(02) 123-4567',
                'event_name' => 'School Fair',
                'weather_condition' => 'heavy rain',
                'recipient_name' => 'Parent/Guardian',
                'sender_name' => 'School Admin',
                'additional_info' => 'More details to follow',
                default => ucwords(str_replace('_', ' ', $variable)),
            };
        }

        return self::format($template->content, $sampleVariables);
    }
}
