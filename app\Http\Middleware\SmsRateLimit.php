<?php

namespace App\Http\Middleware;

use App\Models\SmsRateLimit as SmsRateLimitModel;
use App\Models\SmsUsageTracking;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class SmsRateLimit
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $limitType = 'global', int $limit = 60, string $period = 'minute'): ResponseAlias
    {
        // Skip rate limiting if disabled
        if (!config('services.sms.enable_rate_limiting', true)) {
            return $next($request);
        }

        // Determine identifier based on limit type
        $identifier = $this->getIdentifier($request, $limitType);
        
        // Get or create rate limit record
        $rateLimit = SmsRateLimitModel::firstOrCreate(
            [
                'identifier' => $identifier,
                'type' => $limitType,
                'limit_period' => $period,
            ],
            [
                'limit_count' => $limit,
                'period_start' => now(),
                'period_end' => $this->calculatePeriodEnd(now(), $period),
                'current_count' => 0,
                'is_exceeded' => false,
                'exceeded_count' => 0,
            ]
        );

        // Reset if period expired
        if (!$rateLimit->isActive()) {
            $rateLimit->resetForNewPeriod();
        }

        // Check if rate limit is exceeded
        if ($rateLimit->isExceeded()) {
            return $this->rateLimitExceeded($rateLimit, $request);
        }

        // Process the request
        $response = $next($request);

        // Increment usage count after successful request
        if ($response->getStatusCode() < 400) {
            $rateLimit->incrementUsage();
            
            // Log rate limit usage for teachers
            if ($limitType === 'teacher' && $request->user()) {
                $this->logRateLimitUsage($request->user()->id);
            }
        }

        // Add rate limit headers
        $response->headers->set('X-RateLimit-Limit', $rateLimit->limit_count);
        $response->headers->set('X-RateLimit-Remaining', $rateLimit->getRemainingRequests());
        $response->headers->set('X-RateLimit-Reset', $rateLimit->period_end->timestamp);

        return $response;
    }

    /**
     * Get identifier for rate limiting.
     */
    protected function getIdentifier(Request $request, string $limitType): string
    {
        return match($limitType) {
            'teacher' => 'teacher_' . ($request->user()?->id ?? 'anonymous'),
            'ip' => 'ip_' . $request->ip(),
            'user' => 'user_' . ($request->user()?->id ?? 'anonymous'),
            'global' => 'global',
            default => 'global',
        };
    }

    /**
     * Calculate period end based on period type.
     */
    protected function calculatePeriodEnd(\DateTime $start, string $period): \DateTime
    {
        return match($period) {
            'minute' => (clone $start)->modify('+1 minute'),
            'hour' => (clone $start)->modify('+1 hour'),
            'day' => (clone $start)->modify('+1 day'),
            'month' => (clone $start)->modify('+1 month'),
            default => (clone $start)->modify('+1 hour'),
        };
    }

    /**
     * Handle rate limit exceeded response.
     */
    protected function rateLimitExceeded(SmsRateLimitModel $rateLimit, Request $request): Response
    {
        $retryAfter = $rateLimit->getTimeUntilReset();
        
        // Log rate limit hit for teachers
        if (str_starts_with($rateLimit->identifier, 'teacher_') && $request->user()) {
            $this->logRateLimitUsage($request->user()->id, true);
        }

        $response = response()->json([
            'error' => 'Rate limit exceeded',
            'message' => 'Too many SMS requests. Please try again later.',
            'retry_after' => $retryAfter,
            'limit' => $rateLimit->limit_count,
            'period' => $rateLimit->limit_period,
            'reset_at' => $rateLimit->period_end->toISOString(),
        ], 429);

        $response->headers->set('Retry-After', $retryAfter);
        $response->headers->set('X-RateLimit-Limit', $rateLimit->limit_count);
        $response->headers->set('X-RateLimit-Remaining', 0);
        $response->headers->set('X-RateLimit-Reset', $rateLimit->period_end->timestamp);

        return $response;
    }

    /**
     * Log rate limit usage for tracking.
     */
    protected function logRateLimitUsage(int $teacherId, bool $isHit = false): void
    {
        $tracking = SmsUsageTracking::firstOrCreate([
            'teacher_id' => $teacherId,
            'tracking_date' => now()->toDateString(),
            'tracking_period' => 'daily',
        ]);

        if ($isHit) {
            $tracking->recordRateLimitHit();
        }
    }
}
