<?php

namespace App\Services;

use App\Enums\AttendanceStatus;
use App\Enums\StudentStatus;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Subject;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardAnalyticsService
{
    /**
     * Get comprehensive attendance statistics.
     */
    public function getAttendanceStatistics(string $dateFrom, string $dateTo, array $filters = []): array
    {
        $query = Attendance::with(['student', 'teacher', 'subject'])
            ->whereBetween('date', [$dateFrom, $dateTo]);

        // Apply filters
        if (!empty($filters['grade_level'])) {
            $query->whereHas('student', function ($q) use ($filters) {
                $q->where('grade_level', $filters['grade_level']);
            });
        }

        if (!empty($filters['section'])) {
            $query->whereHas('student', function ($q) use ($filters) {
                $q->where('section', $filters['section']);
            });
        }

        if (!empty($filters['subject_id'])) {
            $query->where('subject_id', $filters['subject_id']);
        }

        $attendanceRecords = $query->get();

        // Calculate statistics
        $totalRecords = $attendanceRecords->count();
        $presentCount = $attendanceRecords->where('status', AttendanceStatus::PRESENT)->count();
        $absentCount = $attendanceRecords->where('status', AttendanceStatus::ABSENT)->count();
        $lateCount = $attendanceRecords->where('status', AttendanceStatus::LATE)->count();
        $excusedCount = $attendanceRecords->where('status', AttendanceStatus::EXCUSED)->count();

        $attendanceRate = $totalRecords > 0 ? round((($presentCount + $lateCount + $excusedCount) / $totalRecords) * 100, 2) : 0;

        // Daily breakdown
        $dailyBreakdown = $attendanceRecords->groupBy(function ($item) {
            return $item->date instanceof Carbon ? $item->date->format('Y-m-d') : $item->date;
        })->map(function ($dayRecords, $date) {
            $total = $dayRecords->count();
            $present = $dayRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();
            
            return [
                'date' => $date,
                'total' => $total,
                'present' => $present,
                'absent' => $dayRecords->where('status', AttendanceStatus::ABSENT)->count(),
                'late' => $dayRecords->where('status', AttendanceStatus::LATE)->count(),
                'excused' => $dayRecords->where('status', AttendanceStatus::EXCUSED)->count(),
                'attendance_rate' => $total > 0 ? round(($present / $total) * 100, 2) : 0,
            ];
        })->values();

        // Grade level breakdown
        $gradeLevelBreakdown = $attendanceRecords->groupBy('student.grade_level')
            ->map(function ($gradeRecords, $gradeLevel) {
                $total = $gradeRecords->count();
                $present = $gradeRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();
                
                return [
                    'grade_level' => $gradeLevel,
                    'total' => $total,
                    'present' => $present,
                    'absent' => $gradeRecords->where('status', AttendanceStatus::ABSENT)->count(),
                    'attendance_rate' => $total > 0 ? round(($present / $total) * 100, 2) : 0,
                ];
            })->values();

        return [
            'summary' => [
                'total_records' => $totalRecords,
                'present_count' => $presentCount,
                'absent_count' => $absentCount,
                'late_count' => $lateCount,
                'excused_count' => $excusedCount,
                'attendance_rate' => $attendanceRate,
            ],
            'daily_breakdown' => $dailyBreakdown,
            'grade_level_breakdown' => $gradeLevelBreakdown,
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo,
            ],
            'filters_applied' => $filters,
        ];
    }

    /**
     * Analyze attendance patterns.
     */
    public function analyzeAttendancePatterns(array $parameters): array
    {
        $query = Attendance::with(['student']);

        if (!empty($parameters['student_id'])) {
            $query->where('student_id', $parameters['student_id']);
        }

        if (!empty($parameters['grade_level']) || !empty($parameters['section'])) {
            $query->whereHas('student', function ($q) use ($parameters) {
                if (!empty($parameters['grade_level'])) {
                    $q->where('grade_level', $parameters['grade_level']);
                }
                if (!empty($parameters['section'])) {
                    $q->where('section', $parameters['section']);
                }
            });
        }

        $days = $parameters['days'] ?? 30;
        $query->where('date', '>=', now()->subDays($days));

        $attendanceRecords = $query->orderBy('date')->get();

        // Analyze patterns
        $patterns = [
            'weekly_patterns' => $this->analyzeWeeklyPatterns($attendanceRecords),
            'time_patterns' => $this->analyzeTimePatterns($attendanceRecords),
            'consistency_score' => $this->calculateConsistencyScore($attendanceRecords),
            'risk_indicators' => $this->identifyRiskIndicators($attendanceRecords),
        ];

        return $patterns;
    }

    /**
     * Get trend analysis for different periods.
     */
    public function getTrendAnalysis(string $period, array $filters = []): array
    {
        $dateRanges = $this->getDateRangesForPeriod($period);
        $trends = [];

        foreach ($dateRanges as $range) {
            $query = Attendance::whereBetween('date', [$range['start'], $range['end']]);

            // Apply filters
            if (!empty($filters['grade_level'])) {
                $query->whereHas('student', function ($q) use ($filters) {
                    $q->where('grade_level', $filters['grade_level']);
                });
            }

            if (!empty($filters['section'])) {
                $query->whereHas('student', function ($q) use ($filters) {
                    $q->where('section', $filters['section']);
                });
            }

            if (!empty($filters['subject_id'])) {
                $query->where('subject_id', $filters['subject_id']);
            }

            $periodData = $query->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status IN ("present", "late", "excused") THEN 1 ELSE 0 END) as present,
                SUM(CASE WHEN status = "absent" THEN 1 ELSE 0 END) as absent
            ')->first();

            $trends[] = [
                'period' => $range['label'],
                'start_date' => $range['start'],
                'end_date' => $range['end'],
                'total' => $periodData->total ?? 0,
                'present' => $periodData->present ?? 0,
                'absent' => $periodData->absent ?? 0,
                'attendance_rate' => $periodData->total > 0 
                    ? round(($periodData->present / $periodData->total) * 100, 2) 
                    : 0,
            ];
        }

        return [
            'period_type' => $period,
            'trends' => $trends,
            'summary' => $this->calculateTrendSummary($trends),
        ];
    }

    /**
     * Get teacher-specific reports.
     */
    public function getTeacherReports(int $teacherId, string $dateFrom, string $dateTo, array $filters = []): array
    {
        $teacher = Teacher::with(['students', 'attendance'])->find($teacherId);
        
        if (!$teacher) {
            throw new \InvalidArgumentException("Teacher not found with ID: {$teacherId}");
        }

        $query = Attendance::where('teacher_id', $teacherId)
            ->whereBetween('date', [$dateFrom, $dateTo]);

        if (!empty($filters['subject_id'])) {
            $query->where('subject_id', $filters['subject_id']);
        }

        $attendanceRecords = $query->with(['student', 'subject'])->get();

        // Calculate teacher statistics
        $totalRecords = $attendanceRecords->count();
        $presentCount = $attendanceRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();
        $absentCount = $attendanceRecords->where('status', AttendanceStatus::ABSENT)->count();

        // Subject breakdown
        $subjectBreakdown = $attendanceRecords->groupBy('subject.name')
            ->map(function ($subjectRecords, $subjectName) {
                $total = $subjectRecords->count();
                $present = $subjectRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();
                
                return [
                    'subject' => $subjectName,
                    'total' => $total,
                    'present' => $present,
                    'absent' => $subjectRecords->where('status', AttendanceStatus::ABSENT)->count(),
                    'attendance_rate' => $total > 0 ? round(($present / $total) * 100, 2) : 0,
                ];
            })->values();

        // Student performance under this teacher
        $studentPerformance = $attendanceRecords->groupBy('student_id')
            ->map(function ($studentRecords) {
                $student = $studentRecords->first()->student;
                $total = $studentRecords->count();
                $present = $studentRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();
                
                return [
                    'student_id' => $student->id,
                    'student_name' => $student->full_name,
                    'grade_level' => $student->grade_level,
                    'section' => $student->section,
                    'total' => $total,
                    'present' => $present,
                    'absent' => $studentRecords->where('status', AttendanceStatus::ABSENT)->count(),
                    'attendance_rate' => $total > 0 ? round(($present / $total) * 100, 2) : 0,
                ];
            })->values();

        return [
            'teacher' => [
                'id' => $teacher->id,
                'name' => $teacher->full_name,
                'email' => $teacher->email,
            ],
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo,
            ],
            'summary' => [
                'total_records' => $totalRecords,
                'present_count' => $presentCount,
                'absent_count' => $absentCount,
                'attendance_rate' => $totalRecords > 0 ? round(($presentCount / $totalRecords) * 100, 2) : 0,
                'total_students' => $attendanceRecords->pluck('student_id')->unique()->count(),
            ],
            'subject_breakdown' => $subjectBreakdown,
            'student_performance' => $studentPerformance->sortByDesc('attendance_rate')->values(),
        ];
    }

    /**
     * Get parent notifications history.
     */
    public function getParentNotifications(array $parameters): array
    {
        // This would typically query a notifications table
        // For now, we'll simulate based on attendance records with SMS events
        $query = Attendance::with(['student']);

        if (!empty($parameters['student_id'])) {
            $query->where('student_id', $parameters['student_id']);
        }

        $dateFrom = $parameters['date_from'] ?? now()->subDays(30)->format('Y-m-d');
        $dateTo = $parameters['date_to'] ?? now()->format('Y-m-d');

        $query->whereBetween('date', [$dateFrom, $dateTo]);

        // Only get records that would trigger SMS notifications
        $query->whereIn('status', [AttendanceStatus::ABSENT, AttendanceStatus::LATE]);

        $perPage = $parameters['per_page'] ?? 20;
        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return [
            'notifications' => $notifications->items(),
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
                'last_page' => $notifications->lastPage(),
            ],
            'summary' => [
                'total_sent' => $notifications->total(),
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
            ],
        ];
    }

    /**
     * Analyze weekly patterns.
     */
    protected function analyzeWeeklyPatterns(Collection $attendanceRecords): array
    {
        $weeklyData = $attendanceRecords->groupBy(function ($record) {
            $date = $record->date instanceof Carbon ? $record->date : Carbon::parse($record->date);
            return $date->format('l'); // Day name
        });

        $patterns = [];
        $daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        foreach ($daysOfWeek as $day) {
            $dayRecords = $weeklyData->get($day, collect());
            $total = $dayRecords->count();
            $present = $dayRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();

            $patterns[] = [
                'day' => $day,
                'total' => $total,
                'present' => $present,
                'absent' => $dayRecords->where('status', AttendanceStatus::ABSENT)->count(),
                'attendance_rate' => $total > 0 ? round(($present / $total) * 100, 2) : 0,
            ];
        }

        return $patterns;
    }

    /**
     * Analyze time patterns.
     */
    protected function analyzeTimePatterns(Collection $attendanceRecords): array
    {
        $timePatterns = $attendanceRecords->groupBy(function ($record) {
            if (!$record->time_in) return 'no_time';

            $time = $record->time_in instanceof Carbon ? $record->time_in : Carbon::parse($record->time_in);
            $hour = $time->hour;

            if ($hour < 8) return 'early';
            if ($hour < 9) return 'on_time';
            if ($hour < 10) return 'slightly_late';
            return 'very_late';
        });

        return [
            'early_arrivals' => $timePatterns->get('early', collect())->count(),
            'on_time_arrivals' => $timePatterns->get('on_time', collect())->count(),
            'slightly_late' => $timePatterns->get('slightly_late', collect())->count(),
            'very_late' => $timePatterns->get('very_late', collect())->count(),
            'no_time_recorded' => $timePatterns->get('no_time', collect())->count(),
        ];
    }

    /**
     * Calculate consistency score.
     */
    protected function calculateConsistencyScore(Collection $attendanceRecords): float
    {
        if ($attendanceRecords->isEmpty()) {
            return 0.0;
        }

        $totalDays = $attendanceRecords->count();
        $presentDays = $attendanceRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();

        // Base score from attendance rate
        $attendanceScore = ($presentDays / $totalDays) * 70;

        // Consistency bonus (less variation in attendance patterns)
        $weeklyConsistency = $this->calculateWeeklyConsistency($attendanceRecords);
        $consistencyBonus = $weeklyConsistency * 30;

        return round($attendanceScore + $consistencyBonus, 2);
    }

    /**
     * Calculate weekly consistency.
     */
    protected function calculateWeeklyConsistency(Collection $attendanceRecords): float
    {
        $weeklyRates = $attendanceRecords->groupBy(function ($record) {
            $date = $record->date instanceof Carbon ? $record->date : Carbon::parse($record->date);
            return $date->format('Y-W'); // Year-Week
        })->map(function ($weekRecords) {
            $total = $weekRecords->count();
            $present = $weekRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count();
            return $total > 0 ? ($present / $total) : 0;
        });

        if ($weeklyRates->count() < 2) {
            return 1.0; // Perfect consistency if only one week
        }

        $mean = $weeklyRates->avg();
        $variance = $weeklyRates->map(function ($rate) use ($mean) {
            return pow($rate - $mean, 2);
        })->avg();

        // Convert variance to consistency score (lower variance = higher consistency)
        return max(0, 1 - ($variance * 4));
    }

    /**
     * Identify risk indicators.
     */
    protected function identifyRiskIndicators(Collection $attendanceRecords): array
    {
        $indicators = [];

        // Recent absence streak
        $recentRecords = $attendanceRecords->sortByDesc('date')->take(5);
        $recentAbsences = $recentRecords->where('status', AttendanceStatus::ABSENT)->count();

        if ($recentAbsences >= 3) {
            $indicators[] = [
                'type' => 'absence_streak',
                'severity' => 'high',
                'description' => 'Multiple recent absences detected',
                'count' => $recentAbsences,
            ];
        }

        // Frequent lateness
        $lateCount = $attendanceRecords->where('status', AttendanceStatus::LATE)->count();
        $totalCount = $attendanceRecords->count();

        if ($totalCount > 0 && ($lateCount / $totalCount) > 0.3) {
            $indicators[] = [
                'type' => 'frequent_lateness',
                'severity' => 'medium',
                'description' => 'Frequently arriving late',
                'rate' => round(($lateCount / $totalCount) * 100, 1),
            ];
        }

        // Declining trend
        $firstHalf = $attendanceRecords->take($attendanceRecords->count() / 2);
        $secondHalf = $attendanceRecords->skip($attendanceRecords->count() / 2);

        $firstHalfRate = $firstHalf->count() > 0 ?
            $firstHalf->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count() / $firstHalf->count() : 0;
        $secondHalfRate = $secondHalf->count() > 0 ?
            $secondHalf->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])->count() / $secondHalf->count() : 0;

        if ($firstHalfRate - $secondHalfRate > 0.2) {
            $indicators[] = [
                'type' => 'declining_trend',
                'severity' => 'high',
                'description' => 'Attendance rate declining over time',
                'decline' => round(($firstHalfRate - $secondHalfRate) * 100, 1),
            ];
        }

        return $indicators;
    }

    /**
     * Get date ranges for different periods.
     */
    protected function getDateRangesForPeriod(string $period): array
    {
        $ranges = [];
        $now = now();

        switch ($period) {
            case 'weekly':
                for ($i = 5; $i >= 0; $i--) {
                    $start = $now->copy()->subWeeks($i)->startOfWeek();
                    $end = $now->copy()->subWeeks($i)->endOfWeek();
                    $ranges[] = [
                        'start' => $start->format('Y-m-d'),
                        'end' => $end->format('Y-m-d'),
                        'label' => $start->format('M d') . ' - ' . $end->format('M d, Y'),
                    ];
                }
                break;

            case 'monthly':
                for ($i = 5; $i >= 0; $i--) {
                    $start = $now->copy()->subMonths($i)->startOfMonth();
                    $end = $now->copy()->subMonths($i)->endOfMonth();
                    $ranges[] = [
                        'start' => $start->format('Y-m-d'),
                        'end' => $end->format('Y-m-d'),
                        'label' => $start->format('F Y'),
                    ];
                }
                break;

            case 'quarterly':
                for ($i = 3; $i >= 0; $i--) {
                    $start = $now->copy()->subQuarters($i)->startOfQuarter();
                    $end = $now->copy()->subQuarters($i)->endOfQuarter();
                    $ranges[] = [
                        'start' => $start->format('Y-m-d'),
                        'end' => $end->format('Y-m-d'),
                        'label' => 'Q' . $start->quarter . ' ' . $start->year,
                    ];
                }
                break;

            default:
                // Default to monthly
                return $this->getDateRangesForPeriod('monthly');
        }

        return $ranges;
    }

    /**
     * Calculate trend summary.
     */
    protected function calculateTrendSummary(array $trends): array
    {
        if (empty($trends)) {
            return [
                'overall_trend' => 'stable',
                'average_attendance_rate' => 0,
                'best_period' => null,
                'worst_period' => null,
            ];
        }

        $rates = array_column($trends, 'attendance_rate');
        $averageRate = array_sum($rates) / count($rates);

        // Determine overall trend
        $firstRate = reset($rates);
        $lastRate = end($rates);
        $trendDirection = 'stable';

        if ($lastRate > $firstRate + 5) {
            $trendDirection = 'improving';
        } elseif ($lastRate < $firstRate - 5) {
            $trendDirection = 'declining';
        }

        // Find best and worst periods
        $bestPeriod = null;
        $worstPeriod = null;
        $maxRate = -1;
        $minRate = 101;

        foreach ($trends as $trend) {
            if ($trend['attendance_rate'] > $maxRate) {
                $maxRate = $trend['attendance_rate'];
                $bestPeriod = $trend;
            }
            if ($trend['attendance_rate'] < $minRate) {
                $minRate = $trend['attendance_rate'];
                $worstPeriod = $trend;
            }
        }

        return [
            'overall_trend' => $trendDirection,
            'average_attendance_rate' => round($averageRate, 2),
            'best_period' => $bestPeriod,
            'worst_period' => $worstPeriod,
            'trend_change' => round($lastRate - $firstRate, 2),
        ];
    }
}
