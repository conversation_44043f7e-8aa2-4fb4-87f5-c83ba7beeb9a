<?php

namespace App\Models;

use App\Enums\SmsProvider;
use App\Enums\SmsStatus;
use App\Enums\SmsType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class SmsLog extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'sms_logs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'message_id',
        'batch_id',
        'teacher_id',
        'recipient_phone',
        'recipient_name',
        'recipient_type',
        'message_content',
        'message_type',
        'template_used',
        'provider',
        'status',
        'provider_response',
        'failure_reason',
        'cost',
        'message_parts',
        'character_count',
        'queued_at',
        'sent_at',
        'delivered_at',
        'failed_at',
        'retry_count',
        'next_retry_at',
        'max_retries',
        'metadata',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'status' => SmsStatus::class,
            'message_type' => SmsType::class,
            'provider' => SmsProvider::class,
            'cost' => 'decimal:4',
            'message_parts' => 'integer',
            'character_count' => 'integer',
            'retry_count' => 'integer',
            'max_retries' => 'integer',
            'metadata' => 'array',
            'provider_response' => 'array',
            'queued_at' => 'datetime',
            'sent_at' => 'datetime',
            'delivered_at' => 'datetime',
            'failed_at' => 'datetime',
            'next_retry_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // RELATIONSHIPS

    /**
     * Get the teacher who sent the SMS.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    // SCOPES

    /**
     * Scope to filter by status.
     */
    public function scopeWithStatus(Builder $query, SmsStatus $status): void
    {
        $query->where('status', $status);
    }

    /**
     * Scope to filter by message type.
     */
    public function scopeWithType(Builder $query, SmsType $type): void
    {
        $query->where('message_type', $type);
    }

    /**
     * Scope to filter by provider.
     */
    public function scopeWithProvider(Builder $query, SmsProvider $provider): void
    {
        $query->where('provider', $provider);
    }

    /**
     * Scope to filter by teacher.
     */
    public function scopeForTeacher(Builder $query, int $teacherId): void
    {
        $query->where('teacher_id', $teacherId);
    }

    /**
     * Scope to filter by recipient phone.
     */
    public function scopeForRecipient(Builder $query, string $phone): void
    {
        $query->where('recipient_phone', $phone);
    }

    /**
     * Scope to filter by batch.
     */
    public function scopeInBatch(Builder $query, string $batchId): void
    {
        $query->where('batch_id', $batchId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange(Builder $query, $startDate, $endDate): void
    {
        $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for failed messages that can be retried.
     */
    public function scopeRetryable(Builder $query): void
    {
        $query->where('status', SmsStatus::FAILED)
              ->where('retry_count', '<', 'max_retries')
              ->where(function ($q) {
                  $q->whereNull('next_retry_at')
                    ->orWhere('next_retry_at', '<=', now());
              });
    }

    // ACCESSORS & MUTATORS

    /**
     * Get the delivery time in seconds.
     */
    public function getDeliveryTimeAttribute(): ?int
    {
        if ($this->sent_at && $this->delivered_at) {
            return $this->delivered_at->diffInSeconds($this->sent_at);
        }
        return null;
    }

    /**
     * Check if message can be retried.
     */
    public function getCanRetryAttribute(): bool
    {
        return $this->status === SmsStatus::FAILED && 
               $this->retry_count < $this->max_retries;
    }

    /**
     * Get formatted cost.
     */
    public function getFormattedCostAttribute(): string
    {
        return '₱' . number_format($this->cost, 2);
    }

    // METHODS

    /**
     * Mark message as sent.
     */
    public function markAsSent(string $messageId = null, array $providerResponse = []): void
    {
        $this->update([
            'status' => SmsStatus::SENT,
            'message_id' => $messageId ?? $this->message_id,
            'sent_at' => now(),
            'provider_response' => $providerResponse,
        ]);
    }

    /**
     * Mark message as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => SmsStatus::DELIVERED,
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark message as failed.
     */
    public function markAsFailed(string $reason, array $details = []): void
    {
        $this->update([
            'status' => SmsStatus::FAILED,
            'failure_reason' => $reason,
            'failed_at' => now(),
            'provider_response' => $details,
        ]);
    }

    /**
     * Increment retry count and set next retry time.
     */
    public function incrementRetry(int $delayMinutes = 5): void
    {
        $this->update([
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => now()->addMinutes($delayMinutes),
        ]);
    }
}
