<?php

namespace App\Console\Commands;

use App\Models\Student;
use App\Services\QRCodeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class QRCodeManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qr:manage
                            {action : Action to perform (generate, regenerate, cleanup, stats)}
                            {--student-id= : Specific student ID for single operations}
                            {--grade= : Grade level filter}
                            {--section= : Section filter}
                            {--batch-size=50 : Batch size for bulk operations}
                            {--days=30 : Days for cleanup operation}
                            {--force : Force operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage QR codes for students (generate, regenerate, cleanup, stats)';

    /**
     * QR Code Service instance
     */
    private QRCodeService $qrCodeService;

    /**
     * Create a new command instance.
     */
    public function __construct(QRCodeService $qrCodeService)
    {
        parent::__construct();
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'generate':
                return $this->handleGenerate();
            case 'regenerate':
                return $this->handleRegenerate();
            case 'cleanup':
                return $this->handleCleanup();
            case 'stats':
                return $this->handleStats();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: generate, regenerate, cleanup, stats');
                return 1;
        }
    }

    /**
     * Handle QR code generation
     */
    private function handleGenerate(): int
    {
        $studentId = $this->option('student-id');
        $grade = $this->option('grade');
        $section = $this->option('section');
        $batchSize = (int) $this->option('batch-size');

        if ($studentId) {
            return $this->generateForStudent($studentId);
        }

        return $this->generateBulk($grade, $section, $batchSize);
    }

    /**
     * Handle QR code regeneration
     */
    private function handleRegenerate(): int
    {
        $studentId = $this->option('student-id');

        if (!$studentId) {
            $this->error('Student ID is required for regeneration');
            return 1;
        }

        $student = Student::where('student_id', $studentId)->first();
        if (!$student) {
            $this->error("Student not found: {$studentId}");
            return 1;
        }

        try {
            $this->info("Regenerating QR code for {$student->full_name}...");
            $filename = $this->qrCodeService->regenerateQR($student);
            $this->info("QR code regenerated successfully: {$filename}");
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to regenerate QR code: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Handle QR code cleanup
     */
    private function handleCleanup(): int
    {
        $days = (int) $this->option('days');
        $force = $this->option('force');

        if (!$force) {
            if (!$this->confirm("This will delete QR code files older than {$days} days. Continue?")) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        try {
            $this->info("Cleaning up QR codes older than {$days} days...");
            $result = $this->qrCodeService->cleanupExpiredQRCodes($days);

            $this->info("Cleanup completed:");
            $this->info("- Files deleted: {$result['deleted_count']}");
            $this->info("- Errors: {$result['error_count']}");

            return 0;
        } catch (\Exception $e) {
            $this->error("Cleanup failed: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Handle QR code statistics
     */
    private function handleStats(): int
    {
        try {
            $stats = $this->qrCodeService->getQRCodeStats();

            $this->info('QR Code Statistics:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Students', $stats['total_students']],
                    ['Students with QR Codes', $stats['students_with_qr']],
                    ['Coverage Percentage', $stats['coverage_percentage'] . '%'],
                    ['Recently Generated (7 days)', $stats['recently_generated']],
                    ['QR Expiration (minutes)', $stats['qr_expiration_minutes']],
                    ['Max Batch Size', $stats['max_batch_size']],
                ]
            );

            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to get statistics: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Generate QR code for a specific student
     */
    private function generateForStudent(string $studentId): int
    {
        $student = Student::where('student_id', $studentId)->first();
        if (!$student) {
            $this->error("Student not found: {$studentId}");
            return 1;
        }

        try {
            $this->info("Generating QR code for {$student->full_name}...");
            $filename = $this->qrCodeService->generateStudentQR($student);
            $this->info("QR code generated successfully: {$filename}");
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to generate QR code: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Generate QR codes in bulk
     */
    private function generateBulk(?string $grade, ?string $section, int $batchSize): int
    {
        try {
            $query = Student::active();

            if ($grade) {
                $query->byGradeLevel($grade);
            }

            if ($section) {
                $query->bySection($section);
            }

            $students = $query->get();
            $totalStudents = $students->count();

            if ($totalStudents === 0) {
                $this->info('No students found matching the criteria.');
                return 0;
            }

            $this->info("Found {$totalStudents} students. Processing in batches of {$batchSize}...");

            $studentIds = $students->pluck('id')->toArray();
            $batches = array_chunk($studentIds, $batchSize);
            $totalSuccess = 0;
            $totalErrors = 0;

            foreach ($batches as $index => $batch) {
                $batchNumber = $index + 1;
                $this->info("Processing batch {$batchNumber}/" . count($batches) . "...");

                $result = $this->qrCodeService->batchGenerateQR($batch);
                $totalSuccess += $result['summary']['success'];
                $totalErrors += $result['summary']['errors'];

                $this->info("Batch {$batchNumber} completed: {$result['summary']['success']} success, {$result['summary']['errors']} errors");
            }

            $this->info("Bulk generation completed:");
            $this->info("- Total processed: {$totalStudents}");
            $this->info("- Successful: {$totalSuccess}");
            $this->info("- Errors: {$totalErrors}");

            return $totalErrors > 0 ? 1 : 0;

        } catch (\Exception $e) {
            $this->error("Bulk generation failed: {$e->getMessage()}");
            return 1;
        }
    }
}
