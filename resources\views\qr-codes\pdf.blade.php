<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student QR Codes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 12px;
        }
        
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .qr-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
            page-break-inside: avoid;
        }
        
        .qr-item img {
            max-width: 150px;
            height: auto;
            margin-bottom: 10px;
        }
        
        .student-info {
            margin-top: 10px;
        }
        
        .student-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .student-details {
            font-size: 11px;
            color: #666;
            line-height: 1.4;
        }
        
        .student-id {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
            margin-top: 5px;
        }
        
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #999;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .qr-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }
            
            .qr-item {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Student QR Codes</h1>
        <p>Generated on {{ $generated_at->format('F j, Y \a\t g:i A') }}</p>
        <p>Total QR Codes: {{ count($qr_codes) }}</p>
    </div>

    <div class="qr-grid">
        @foreach($qr_codes as $index => $qr_code)
            @if($index > 0 && $index % 12 == 0)
                </div>
                <div class="page-break"></div>
                <div class="qr-grid">
            @endif
            
            <div class="qr-item">
                <img src="{{ $qr_code['qr_path'] }}" alt="QR Code for {{ $qr_code['student']->full_name }}">
                
                <div class="student-info">
                    <div class="student-name">{{ $qr_code['student']->full_name }}</div>
                    <div class="student-details">
                        Grade {{ $qr_code['student']->grade_level }} - {{ $qr_code['student']->section }}<br>
                        <span class="student-id">{{ $qr_code['student']->student_id }}</span>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <div class="footer">
        <p>
            QR Code System - {{ config('app.name') }} | 
            Generated: {{ $generated_at->format('Y-m-d H:i:s') }} | 
            Page <span class="pagenum"></span>
        </p>
    </div>
</body>
</html>
