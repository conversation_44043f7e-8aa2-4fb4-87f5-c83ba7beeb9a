<?php

namespace App\Http\Requests;

use App\Enums\SmsType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendBulkSmsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'recipients' => [
                'required',
                'array',
                'min:1',
                'max:1000', // Limit bulk size
            ],
            'recipients.*' => [
                'required',
                'array',
            ],
            'recipients.*.phone' => [
                'required',
                'string',
                'regex:/^(\+63|63|0)?9\d{9}$/',
            ],
            'recipients.*.name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'recipients.*.variables' => [
                'nullable',
                'array',
            ],
            'message' => [
                'required',
                'string',
                'max:1600',
            ],
            'type' => [
                'nullable',
                'string',
                Rule::in(array_column(SmsType::cases(), 'value')),
            ],
            'template_name' => [
                'nullable',
                'string',
                'exists:sms_templates,name',
            ],
            'queue' => [
                'nullable',
                'boolean',
            ],
            'scheduled_at' => [
                'nullable',
                'date',
                'after:now',
            ],
            'priority' => [
                'nullable',
                'integer',
                'min:1',
                'max:10',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'recipients.required' => 'Recipients list is required.',
            'recipients.min' => 'At least one recipient is required.',
            'recipients.max' => 'Cannot send to more than 1000 recipients at once.',
            'recipients.*.phone.required' => 'Phone number is required for each recipient.',
            'recipients.*.phone.regex' => 'Please provide valid Philippine mobile numbers (e.g., +639123456789, 09123456789).',
            'message.required' => 'Message content is required.',
            'message.max' => 'Message cannot exceed 1600 characters.',
            'type.in' => 'Invalid SMS type provided.',
            'template_name.exists' => 'The specified template does not exist.',
            'scheduled_at.after' => 'Scheduled time must be in the future.',
            'priority.min' => 'Priority must be between 1 and 10.',
            'priority.max' => 'Priority must be between 1 and 10.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and format phone numbers for all recipients
        if ($this->has('recipients') && is_array($this->recipients)) {
            $recipients = $this->recipients;
            
            foreach ($recipients as &$recipient) {
                if (isset($recipient['phone'])) {
                    $phone = preg_replace('/[^0-9+]/', '', $recipient['phone']);
                    
                    // Convert to international format
                    if (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
                        $phone = '+63' . substr($phone, 1);
                    } elseif (strlen($phone) === 10 && substr($phone, 0, 1) === '9') {
                        $phone = '+63' . $phone;
                    } elseif (strlen($phone) === 12 && substr($phone, 0, 2) === '63') {
                        $phone = '+' . $phone;
                    }
                    
                    $recipient['phone'] = $phone;
                }
            }
            
            $this->merge(['recipients' => $recipients]);
        }

        // Set default values
        $defaults = [];
        
        if (!$this->has('type')) {
            $defaults['type'] = 'bulk';
        }
        
        if (!$this->has('queue')) {
            $defaults['queue'] = true;
        }
        
        if (!$this->has('priority')) {
            $defaults['priority'] = 5;
        }
        
        if (!empty($defaults)) {
            $this->merge($defaults);
        }
    }

    /**
     * Get the validated data with additional processing.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Ensure recipients have consistent structure
        if (isset($validated['recipients'])) {
            foreach ($validated['recipients'] as &$recipient) {
                $recipient['name'] = $recipient['name'] ?? null;
                $recipient['variables'] = $recipient['variables'] ?? [];
            }
        }
        
        return $validated;
    }
}
