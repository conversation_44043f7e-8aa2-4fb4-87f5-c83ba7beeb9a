<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_templates', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Template identification
            $table->string('name')->unique()->comment('Template name/identifier');
            $table->string('display_name')->comment('Human-readable template name');
            $table->text('description')->nullable()->comment('Template description');
            
            // Template content
            $table->text('content')->comment('Template content with placeholders');
            $table->json('variables')->nullable()->comment('Available template variables');
            $table->text('sample_output')->nullable()->comment('Sample rendered output');
            
            // Template categorization
            $table->string('category')->default('general')->comment('attendance, alert, announcement, general');
            $table->string('type')->default('notification')->comment('notification, reminder, alert, announcement');
            
            // Usage and validation
            $table->boolean('is_active')->default(true)->comment('Template is active');
            $table->integer('character_limit')->default(160)->comment('Character limit for template');
            $table->integer('usage_count')->default(0)->comment('Number of times used');
            
            // Ownership and permissions
            $table->foreignId('created_by')
                  ->nullable()
                  ->constrained('teachers')
                  ->onDelete('set null')
                  ->comment('Teacher who created template');
            $table->boolean('is_system')->default(false)->comment('System template (cannot be deleted)');
            $table->json('allowed_roles')->nullable()->comment('Roles allowed to use template');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['category', 'is_active']); // Category filtering
            $table->index(['type', 'is_active']); // Type filtering
            $table->index(['is_active', 'created_at']); // Active templates
            $table->index(['created_by']); // Creator filtering
            $table->index(['usage_count']); // Popular templates
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_templates');
    }
};
