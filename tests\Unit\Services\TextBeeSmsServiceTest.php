<?php

namespace Tests\Unit\Services;

use App\Enums\SmsProvider;
use App\Enums\SmsStatus;
use App\Enums\SmsType;
use App\Models\Attendance;
use App\Models\SmsLog;
use App\Models\SmsTemplate;
use App\Models\Student;
use App\Models\Teacher;
use App\Services\TextBeeSmsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class TextBeeSmsServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TextBeeSmsService $smsService;
    protected Teacher $teacher;
    protected Student $student;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->smsService = new TextBeeSmsService();
        
        // Create test data
        $this->teacher = Teacher::factory()->create();
        $this->student = Student::factory()->create([
            'parent_phone' => '+639123456789',
        ]);

        // Mock SMS configuration
        Config::set('services.sms', [
            'textbee' => [
                'api_key' => 'test-api-key',
                'sender_name' => 'TEST_SCHOOL',
                'base_url' => 'https://api.textbee.com',
                'cost_per_sms' => 1.00,
            ],
            'default_provider' => 'textbee',
            'enabled' => true,
        ]);
    }

    /** @test */
    public function it_can_send_sms_successfully()
    {
        // Mock successful HTTP response
        Http::fake([
            'api.textbee.com/*' => Http::response([
                'success' => true,
                'message_id' => 'msg_123456',
                'status' => 'sent',
                'cost' => 1.00,
            ], 200)
        ]);

        $result = $this->smsService->sendSMS(
            phone: '+639123456789',
            message: 'Test message',
            type: SmsType::MANUAL,
            teacherId: $this->teacher->id,
            recipientName: 'Test Parent',
            recipientType: 'parent'
        );

        $this->assertTrue($result['success']);
        $this->assertEquals('msg_123456', $result['message_id']);
        $this->assertEquals(1.00, $result['cost']);

        // Verify SMS log was created
        $this->assertDatabaseHas('sms_logs', [
            'teacher_id' => $this->teacher->id,
            'recipient_phone' => '+639123456789',
            'message_type' => SmsType::MANUAL->value,
            'status' => SmsStatus::SENT->value,
            'provider' => SmsProvider::TEXTBEE->value,
        ]);
    }

    /** @test */
    public function it_handles_failed_sms_sending()
    {
        // Mock failed HTTP response
        Http::fake([
            'api.textbee.com/*' => Http::response([
                'success' => false,
                'error' => 'Invalid phone number',
            ], 400)
        ]);

        $result = $this->smsService->sendSMS(
            phone: 'invalid-phone',
            message: 'Test message',
            type: SmsType::MANUAL,
            teacherId: $this->teacher->id
        );

        $this->assertFalse($result['success']);
        $this->assertStringContains('Invalid phone number', $result['error']);

        // Verify SMS log was created with failed status
        $this->assertDatabaseHas('sms_logs', [
            'teacher_id' => $this->teacher->id,
            'recipient_phone' => 'invalid-phone',
            'status' => SmsStatus::FAILED->value,
        ]);
    }

    /** @test */
    public function it_validates_philippine_phone_numbers()
    {
        $validNumbers = [
            '+639123456789',
            '639123456789',
            '09123456789',
        ];

        $invalidNumbers = [
            '123456789',
            '+1234567890',
            'invalid',
            '',
        ];

        foreach ($validNumbers as $phone) {
            $this->assertTrue($this->smsService->validatePhone($phone), "Failed to validate: {$phone}");
        }

        foreach ($invalidNumbers as $phone) {
            $this->assertFalse($this->smsService->validatePhone($phone), "Incorrectly validated: {$phone}");
        }
    }

    /** @test */
    public function it_can_send_attendance_alert()
    {
        // Create attendance record
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => \App\Enums\AttendanceStatus::ABSENT,
        ]);

        // Create SMS template
        SmsTemplate::factory()->create([
            'name' => 'attendance_absent',
            'content' => 'ALERT: Your child {{student_name}} is marked absent for {{subject}} on {{date}}.',
            'type' => SmsType::ATTENDANCE,
        ]);

        // Mock successful HTTP response
        Http::fake([
            'api.textbee.com/*' => Http::response([
                'success' => true,
                'message_id' => 'msg_attendance_123',
                'status' => 'sent',
                'cost' => 1.00,
            ], 200)
        ]);

        $result = $this->smsService->sendAttendanceAlert($attendance);

        $this->assertTrue($result['success']);
        $this->assertEquals('msg_attendance_123', $result['message_id']);

        // Verify SMS log contains attendance metadata
        $this->assertDatabaseHas('sms_logs', [
            'teacher_id' => $this->teacher->id,
            'message_type' => SmsType::ATTENDANCE->value,
            'status' => SmsStatus::SENT->value,
        ]);
    }

    /** @test */
    public function it_can_send_bulk_sms()
    {
        $recipients = [
            ['phone' => '+639123456789', 'name' => 'Parent 1'],
            ['phone' => '+639987654321', 'name' => 'Parent 2'],
        ];

        // Mock successful HTTP responses
        Http::fake([
            'api.textbee.com/*' => Http::response([
                'success' => true,
                'message_id' => 'msg_bulk_123',
                'status' => 'queued',
                'cost' => 1.00,
            ], 200)
        ]);

        $result = $this->smsService->sendBulkSMS(
            recipients: $recipients,
            message: 'Bulk test message',
            teacherId: $this->teacher->id
        );

        $this->assertTrue($result['success']);
        $this->assertEquals(2, $result['total_recipients']);
        $this->assertEquals(2, $result['queued_count']);

        // Verify queue entries were created
        $this->assertDatabaseCount('sms_queue', 2);
    }

    /** @test */
    public function it_can_get_delivery_status()
    {
        // Create SMS log
        $smsLog = SmsLog::factory()->create([
            'message_id' => 'msg_status_test',
            'status' => SmsStatus::SENT,
        ]);

        // Mock status check response
        Http::fake([
            'api.textbee.com/*' => Http::response([
                'success' => true,
                'message_id' => 'msg_status_test',
                'status' => 'delivered',
                'delivered_at' => now()->toISOString(),
            ], 200)
        ]);

        $result = $this->smsService->getDeliveryStatus('msg_status_test');

        $this->assertTrue($result['success']);
        $this->assertEquals('delivered', $result['status']);

        // Verify SMS log was updated
        $smsLog->refresh();
        $this->assertEquals(SmsStatus::DELIVERED, $smsLog->status);
    }

    /** @test */
    public function it_formats_messages_with_templates()
    {
        $template = 'Hello {{name}}, your child {{student_name}} is {{status}}.';
        $variables = [
            'name' => 'John Doe',
            'student_name' => 'Jane Doe',
            'status' => 'present',
        ];

        $result = $this->smsService->formatMessage($template, $variables);

        $expected = 'Hello John Doe, your child Jane Doe is present.';
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_logs_sms_usage()
    {
        $this->smsService->logSMSUsage(
            teacherId: $this->teacher->id,
            messageCount: 5,
            cost: 5.00,
            messageType: SmsType::BULK
        );

        $this->assertDatabaseHas('sms_usage_tracking', [
            'teacher_id' => $this->teacher->id,
            'total_messages' => 5,
            'total_cost' => 5.00,
        ]);
    }

    /** @test */
    public function it_queues_sms_for_later_sending()
    {
        $result = $this->smsService->queueSMS(
            phone: '+639123456789',
            message: 'Queued message',
            teacherId: $this->teacher->id,
            scheduledAt: now()->addHour()
        );

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['queue_id']);

        $this->assertDatabaseHas('sms_queue', [
            'recipient_phone' => '+639123456789',
            'message_content' => 'Queued message',
            'teacher_id' => $this->teacher->id,
            'status' => 'pending',
        ]);
    }
}
