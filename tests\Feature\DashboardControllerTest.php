<?php

namespace Tests\Feature;

use App\Enums\AttendanceStatus;
use App\Enums\StudentStatus;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Subject;
use App\Models\User;
use App\Services\AttendanceReportService;
use App\Services\AttendanceExportService;
use App\Services\DashboardAnalyticsService;
use App\Services\StudentRiskAssessmentService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class DashboardControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Teacher $teacher;
    protected Student $student;
    protected Subject $subject;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();
        
        // Create test teacher
        $this->teacher = Teacher::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Create test student
        $this->student = Student::factory()->create([
            'status' => StudentStatus::ACTIVE,
            'grade_level' => '10',
            'section' => 'A',
        ]);

        // Create test subject
        $this->subject = Subject::factory()->create([
            'name' => 'Mathematics',
            'is_active' => true,
        ]);

        // Create some test attendance records
        $this->createTestAttendanceRecords();
    }

    protected function createTestAttendanceRecords(): void
    {
        $dates = [
            now()->subDays(5),
            now()->subDays(4),
            now()->subDays(3),
            now()->subDays(2),
            now()->subDays(1),
            now(),
        ];

        foreach ($dates as $date) {
            Attendance::factory()->create([
                'student_id' => $this->student->id,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'date' => $date->format('Y-m-d'),
                'status' => $this->faker->randomElement([
                    AttendanceStatus::PRESENT,
                    AttendanceStatus::ABSENT,
                    AttendanceStatus::LATE,
                ]),
                'time_in' => $date->setTime(8, 0),
                'time_out' => $date->setTime(17, 0),
            ]);
        }
    }

    /** @test */
    public function it_displays_dashboard_index_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('dashboard.index'));

        $response->assertStatus(200);
        $response->assertViewIs('dashboard.index');
        $response->assertViewHas('dashboardData');
    }

    /** @test */
    public function it_returns_dashboard_data_as_json()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.index'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'key_metrics',
                'today_attendance',
                'weekly_trends',
                'at_risk_students',
                'recent_activities',
                'chart_data',
                'last_updated',
            ],
        ]);
    }

    /** @test */
    public function it_gets_attendance_statistics()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.attendance.stats'), [
                'date_from' => now()->subDays(30)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d'),
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'summary',
                'daily_breakdown',
                'grade_level_breakdown',
                'period',
                'filters_applied',
            ],
        ]);
    }

    /** @test */
    public function it_gets_student_risk_assessment()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.student.risk'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'assessments',
                'summary',
                'recommendations',
                'filters_applied',
            ],
        ]);
    }

    /** @test */
    public function it_gets_attendance_traits()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.attendance.traits'), [
                'student_id' => $this->student->id,
                'days' => 30,
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'weekly_patterns',
                'time_patterns',
                'consistency_score',
                'risk_indicators',
            ],
        ]);
    }

    /** @test */
    public function it_gets_trend_analysis()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.trend.analysis'), [
                'period' => 'monthly',
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'period_type',
                'trends',
                'summary',
            ],
        ]);
    }

    /** @test */
    public function it_generates_sf2_report()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.generate.sf2'), [
                'grade_level' => '10',
                'section' => 'A',
                'subject_id' => $this->subject->id,
                'date_from' => now()->subDays(30)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d'),
                'format' => 'pdf',
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'report_path',
                'download_url',
                'format',
                'generated_at',
            ],
        ]);
    }

    /** @test */
    public function it_generates_sf4_report()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.generate.sf4'), [
                'grade_level' => '10',
                'section' => 'A',
                'subject_id' => $this->subject->id,
                'date_from' => now()->subDays(30)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d'),
                'format' => 'excel',
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'report_path',
                'download_url',
                'format',
                'generated_at',
            ],
        ]);
    }

    /** @test */
    public function it_gets_teacher_reports()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.teacher.reports'), [
                'teacher_id' => $this->teacher->id,
                'date_from' => now()->subDays(30)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d'),
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'teacher',
                'period',
                'summary',
                'subject_breakdown',
                'student_performance',
            ],
        ]);
    }

    /** @test */
    public function it_gets_parent_notifications()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.parent.notifications'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'notifications',
                'pagination',
                'summary',
            ],
        ]);
    }

    /** @test */
    public function it_exports_dashboard_data()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.export'), [
                'export_type' => 'overview',
                'format' => 'csv',
                'date_from' => now()->subDays(30)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d'),
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'export_type',
                'format',
                'file_path',
                'download_url',
                'generated_at',
            ],
        ]);
    }

    /** @test */
    public function it_validates_sf2_generation_parameters()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.generate.sf2'), [
                // Missing required parameters
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'grade_level',
            'section',
            'subject_id',
            'date_from',
            'date_to',
        ]);
    }

    /** @test */
    public function it_validates_export_parameters()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.export'), [
                'export_type' => 'invalid_type',
                'format' => 'invalid_format',
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'export_type',
            'format',
        ]);
    }

    /** @test */
    public function it_caches_dashboard_data()
    {
        Cache::flush();

        // First request should cache the data
        $response1 = $this->actingAs($this->user)
            ->getJson(route('dashboard.index'));

        $response1->assertStatus(200);

        // Second request should use cached data
        $response2 = $this->actingAs($this->user)
            ->getJson(route('dashboard.index'));

        $response2->assertStatus(200);
        
        // Verify cache was used (same data structure)
        $this->assertEquals(
            $response1->json('data.key_metrics'),
            $response2->json('data.key_metrics')
        );
    }

    /** @test */
    public function it_handles_errors_gracefully()
    {
        // Mock a service to throw an exception
        $this->mock(DashboardAnalyticsService::class, function ($mock) {
            $mock->shouldReceive('getAttendanceStatistics')
                 ->andThrow(new \Exception('Service error'));
        });

        $response = $this->actingAs($this->user)
            ->getJson(route('dashboard.attendance.stats'));

        $response->assertStatus(500);
        $response->assertJson([
            'success' => false,
            'message' => 'Failed to retrieve attendance statistics',
        ]);
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->getJson(route('dashboard.index'));
        $response->assertStatus(401);

        $response = $this->getJson(route('dashboard.attendance.stats'));
        $response->assertStatus(401);

        $response = $this->postJson(route('dashboard.generate.sf2'), []);
        $response->assertStatus(401);
    }
}
