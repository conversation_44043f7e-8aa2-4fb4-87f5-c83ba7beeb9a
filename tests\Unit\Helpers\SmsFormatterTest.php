<?php

namespace Tests\Unit\Helpers;

use App\Helpers\SmsFormatter;
use Tests\TestCase;

class SmsFormatterTest extends TestCase
{
    /** @test */
    public function it_formats_messages_with_variables()
    {
        $template = 'Hello {{name}}, your child {{student_name}} is {{status}}.';
        $variables = [
            'name' => '<PERSON>',
            'student_name' => '<PERSON>',
            'status' => 'present',
        ];

        $result = SmsFormatter::format($template, $variables);

        $expected = 'Hello <PERSON>, your child <PERSON> is present.';
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_handles_missing_variables()
    {
        $template = 'Hello {{name}}, your child {{student_name}} is {{status}}.';
        $variables = [
            'name' => '<PERSON>',
            // Missing student_name and status
        ];

        $result = SmsFormatter::format($template, $variables);

        $expected = 'Hello <PERSON>, your child {{student_name}} is {{status}}.';
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_formats_attendance_messages()
    {
        $result = SmsFormatter::formatAttendanceMessage(
            studentName: '<PERSON>',
            parentName: 'John Doe',
            status: 'absent',
            subject: 'Mathematics',
            date: '2024-01-15',
            time: '08:00 AM',
            schoolName: 'Test School'
        );

        $this->assertStringContains('Jane Doe', $result);
        $this->assertStringContains('absent', $result);
        $this->assertStringContains('Mathematics', $result);
        $this->assertStringContains('2024-01-15', $result);
        $this->assertStringContains('Test School', $result);
    }

    /** @test */
    public function it_formats_emergency_alerts()
    {
        $result = SmsFormatter::formatEmergencyAlert(
            message: 'School closure due to weather',
            schoolName: 'Test School',
            contactInfo: '123-456-7890'
        );

        $this->assertStringContains('EMERGENCY', $result);
        $this->assertStringContains('School closure due to weather', $result);
        $this->assertStringContains('Test School', $result);
        $this->assertStringContains('123-456-7890', $result);
    }

    /** @test */
    public function it_calculates_sms_parts_correctly()
    {
        // Single part message (160 characters or less)
        $shortMessage = str_repeat('a', 160);
        $this->assertEquals(1, SmsFormatter::calculateParts($shortMessage));

        // Multi-part message (over 160 characters)
        $longMessage = str_repeat('a', 320);
        $this->assertEquals(3, SmsFormatter::calculateParts($longMessage)); // 320 chars = 3 parts of 153 chars each

        // Empty message
        $this->assertEquals(1, SmsFormatter::calculateParts(''));

        // Message with special characters (counts as 2 bytes each)
        $unicodeMessage = 'Hello 😀 World';
        $parts = SmsFormatter::calculateParts($unicodeMessage);
        $this->assertGreaterThanOrEqual(1, $parts);
    }

    /** @test */
    public function it_estimates_cost_correctly()
    {
        // Single part message
        $shortMessage = str_repeat('a', 160);
        $cost = SmsFormatter::estimateCost($shortMessage, 1.50);
        $this->assertEquals(1.50, $cost);

        // Multi-part message
        $longMessage = str_repeat('a', 320);
        $cost = SmsFormatter::estimateCost($longMessage, 1.50);
        $this->assertEquals(4.50, $cost); // 3 parts × 1.50

        // Custom cost per SMS
        $cost = SmsFormatter::estimateCost($shortMessage, 2.00);
        $this->assertEquals(2.00, $cost);
    }

    /** @test */
    public function it_gets_sample_output_for_templates()
    {
        $sampleOutput = SmsFormatter::getSampleOutput('attendance_absent');
        
        $this->assertNotNull($sampleOutput);
        $this->assertIsString($sampleOutput);
        $this->assertStringContains('absent', strtolower($sampleOutput));
    }

    /** @test */
    public function it_returns_null_for_unknown_templates()
    {
        $sampleOutput = SmsFormatter::getSampleOutput('unknown_template');
        
        $this->assertNull($sampleOutput);
    }

    /** @test */
    public function it_validates_template_variables()
    {
        $template = 'Hello {{name}}, your child {{student_name}} is {{status}}.';
        $requiredVars = ['name', 'student_name', 'status'];
        
        $validVariables = [
            'name' => 'John Doe',
            'student_name' => 'Jane Doe',
            'status' => 'present',
        ];
        
        $invalidVariables = [
            'name' => 'John Doe',
            // Missing required variables
        ];

        $this->assertTrue(SmsFormatter::validateTemplateVariables($template, $validVariables));
        $this->assertFalse(SmsFormatter::validateTemplateVariables($template, $invalidVariables));
    }

    /** @test */
    public function it_extracts_variables_from_template()
    {
        $template = 'Hello {{name}}, your child {{student_name}} is {{status}} on {{date}}.';
        
        $variables = SmsFormatter::extractTemplateVariables($template);
        
        $expected = ['name', 'student_name', 'status', 'date'];
        $this->assertEquals($expected, $variables);
    }

    /** @test */
    public function it_handles_duplicate_variables_in_template()
    {
        $template = 'Hello {{name}}, {{name}} your child {{student_name}} is {{status}}.';
        
        $variables = SmsFormatter::extractTemplateVariables($template);
        
        $expected = ['name', 'student_name', 'status'];
        $this->assertEquals($expected, $variables);
    }

    /** @test */
    public function it_sanitizes_message_content()
    {
        $dirtyMessage = "Hello\r\n\tWorld  with   extra   spaces";
        
        $cleanMessage = SmsFormatter::sanitizeMessage($dirtyMessage);
        
        $this->assertEquals('Hello World with extra spaces', $cleanMessage);
    }

    /** @test */
    public function it_truncates_long_messages()
    {
        $longMessage = str_repeat('a', 2000);
        
        $truncated = SmsFormatter::truncateMessage($longMessage, 500);
        
        $this->assertEquals(500, strlen($truncated));
        $this->assertStringEndsWith('...', $truncated);
    }

    /** @test */
    public function it_does_not_truncate_short_messages()
    {
        $shortMessage = 'Hello World';
        
        $result = SmsFormatter::truncateMessage($shortMessage, 500);
        
        $this->assertEquals($shortMessage, $result);
    }

    /** @test */
    public function it_formats_phone_numbers()
    {
        $testCases = [
            '09123456789' => '+639123456789',
            '639123456789' => '+639123456789',
            '+639123456789' => '+639123456789',
            '9123456789' => '+639123456789',
        ];

        foreach ($testCases as $input => $expected) {
            $result = SmsFormatter::formatPhoneNumber($input);
            $this->assertEquals($expected, $result, "Failed to format: {$input}");
        }
    }

    /** @test */
    public function it_handles_invalid_phone_numbers()
    {
        $invalidNumbers = [
            '123456789',
            'invalid',
            '',
            '+1234567890',
        ];

        foreach ($invalidNumbers as $invalid) {
            $result = SmsFormatter::formatPhoneNumber($invalid);
            $this->assertNull($result, "Should return null for: {$invalid}");
        }
    }
}
