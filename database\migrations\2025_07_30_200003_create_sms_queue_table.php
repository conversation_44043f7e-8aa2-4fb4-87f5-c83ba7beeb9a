<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_queue', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Queue identification
            $table->string('batch_id')->nullable()->comment('Batch ID for grouped messages');
            $table->integer('priority')->default(5)->comment('Queue priority (1=highest, 10=lowest)');
            
            // Sender information
            $table->foreignId('teacher_id')
                  ->nullable()
                  ->constrained('teachers')
                  ->onDelete('set null')
                  ->comment('Teacher who queued the message');
            
            // Recipient information
            $table->string('recipient_phone', 20)->comment('Recipient phone number');
            $table->string('recipient_name')->nullable()->comment('Recipient name');
            $table->string('recipient_type')->default('parent')->comment('parent, student, teacher, admin');
            
            // Message content
            $table->text('message_content')->comment('SMS message content');
            $table->string('message_type')->default('manual')->comment('attendance, bulk, manual, alert');
            $table->string('template_used')->nullable()->comment('Template name if used');
            
            // Queue processing
            $table->enum('status', ['pending', 'processing', 'processed', 'failed', 'cancelled'])
                  ->default('pending')
                  ->comment('Queue processing status');
            $table->timestamp('scheduled_at')->nullable()->comment('When to send the message');
            $table->timestamp('processed_at')->nullable()->comment('When message was processed');
            
            // Provider settings
            $table->string('provider')->default('textbee')->comment('SMS provider to use');
            $table->json('provider_options')->nullable()->comment('Provider-specific options');
            
            // Retry configuration
            $table->integer('max_retries')->default(3)->comment('Maximum retry attempts');
            $table->integer('retry_delay')->default(300)->comment('Retry delay in seconds');
            
            // Error handling
            $table->text('error_message')->nullable()->comment('Error message if processing failed');
            $table->json('error_details')->nullable()->comment('Detailed error information');
            
            // Metadata
            $table->json('metadata')->nullable()->comment('Additional message metadata');
            $table->string('ip_address')->nullable()->comment('IP address of sender');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for queue processing
            $table->index(['status', 'priority', 'scheduled_at']); // Queue processing order
            $table->index(['batch_id', 'status']); // Batch processing
            $table->index(['teacher_id', 'created_at']); // Teacher queue history
            $table->index(['scheduled_at', 'status']); // Scheduled message processing
            $table->index(['status', 'created_at']); // Status-based queries
            $table->index(['priority']); // Priority-based processing
            $table->index(['provider']); // Provider-based processing
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_queue');
    }
};
