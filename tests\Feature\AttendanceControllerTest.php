<?php

namespace Tests\Feature;

use App\Enums\AttendanceStatus;
use App\Enums\StudentStatus;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AttendanceControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Teacher $teacher;
    protected Student $student;
    protected Subject $subject;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and teacher
        $this->user = User::factory()->create();
        $this->teacher = Teacher::factory()->create(['user_id' => $this->user->id]);
        
        // Create test student and subject
        $this->student = Student::factory()->create(['status' => StudentStatus::ACTIVE]);
        $this->subject = Subject::factory()->create();
    }

    public function test_scan_qr_creates_attendance_record()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('attendance.scan-qr'), [
                'qr_data' => $this->student->qr_code_hash,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'latitude' => 14.5995,
                'longitude' => 120.9842,
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Attendance recorded successfully',
                ]);

        $this->assertDatabaseHas('attendance', [
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'date' => today()->format('Y-m-d'),
        ]);
    }

    public function test_scan_qr_prevents_duplicate_attendance()
    {
        // Create existing attendance
        Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'date' => today(),
            'status' => AttendanceStatus::PRESENT,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('attendance.scan-qr'), [
                'qr_data' => $this->student->qr_code_hash,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
            ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Attendance already recorded for this student today',
                ]);
    }

    public function test_mark_attendance_creates_new_record()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('attendance.mark'), [
                'student_id' => $this->student->id,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'date' => today()->format('Y-m-d'),
                'status' => AttendanceStatus::PRESENT->value,
                'time_in' => '08:00:00',
                'remarks' => 'Manual attendance marking',
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'action' => 'created',
                ]);

        $this->assertDatabaseHas('attendance', [
            'student_id' => $this->student->id,
            'status' => AttendanceStatus::PRESENT->value,
            'remarks' => 'Manual attendance marking',
        ]);
    }

    public function test_get_class_attendance_returns_correct_data()
    {
        // Create some attendance records
        $students = Student::factory()->count(3)->create([
            'grade_level' => '10',
            'section' => 'A',
            'status' => StudentStatus::ACTIVE,
        ]);

        foreach ($students as $student) {
            Attendance::factory()->create([
                'student_id' => $student->id,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'date' => today(),
                'status' => AttendanceStatus::PRESENT,
            ]);
        }

        $response = $this->actingAs($this->user)
            ->getJson(route('attendance.class'), [
                'grade_level' => '10',
                'section' => 'A',
                'subject_id' => $this->subject->id,
                'date' => today()->format('Y-m-d'),
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'grade_level',
                    'section',
                    'date',
                    'statistics' => [
                        'total_students',
                        'present_count',
                        'absent_count',
                        'late_count',
                        'attendance_rate',
                    ],
                    'students',
                    'last_updated',
                ]);
    }

    public function test_update_attendance_modifies_existing_record()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'date' => today(),
            'status' => AttendanceStatus::PRESENT,
        ]);

        $response = $this->actingAs($this->user)
            ->putJson(route('attendance.update', $attendance), [
                'status' => AttendanceStatus::LATE->value,
                'time_in' => '08:30:00',
                'remarks' => 'Updated to late',
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Attendance updated successfully',
                ]);

        $attendance->refresh();
        $this->assertEquals(AttendanceStatus::LATE, $attendance->status);
        $this->assertEquals('Updated to late', $attendance->remarks);
    }

    public function test_bulk_attendance_processes_multiple_students()
    {
        $students = Student::factory()->count(3)->create(['status' => StudentStatus::ACTIVE]);
        
        $studentsData = $students->map(function ($student) {
            return [
                'student_id' => $student->id,
                'status' => AttendanceStatus::PRESENT->value,
                'time_in' => '08:00:00',
            ];
        })->toArray();

        $response = $this->actingAs($this->user)
            ->postJson(route('attendance.bulk'), [
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'date' => today()->format('Y-m-d'),
                'students' => $studentsData,
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'processed' => 3,
                    'created' => 3,
                    'updated' => 0,
                    'errors' => 0,
                ]);

        foreach ($students as $student) {
            $this->assertDatabaseHas('attendance', [
                'student_id' => $student->id,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'date' => today()->format('Y-m-d'),
                'status' => AttendanceStatus::PRESENT->value,
            ]);
        }
    }

    public function test_get_attendance_history_returns_paginated_results()
    {
        // Create attendance records for different dates
        for ($i = 0; $i < 5; $i++) {
            Attendance::factory()->create([
                'student_id' => $this->student->id,
                'teacher_id' => $this->teacher->id,
                'subject_id' => $this->subject->id,
                'date' => today()->subDays($i),
                'status' => AttendanceStatus::PRESENT,
            ]);
        }

        $response = $this->actingAs($this->user)
            ->getJson(route('attendance.history'), [
                'student_id' => $this->student->id,
                'per_page' => 3,
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data',
                    'current_page',
                    'per_page',
                    'total',
                    'last_page',
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    public function test_get_analytics_returns_statistics()
    {
        // Create various attendance records
        Attendance::factory()->count(5)->create([
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'date' => today(),
            'status' => AttendanceStatus::PRESENT,
        ]);

        Attendance::factory()->count(2)->create([
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'date' => today(),
            'status' => AttendanceStatus::ABSENT,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson(route('attendance.analytics'), [
                'subject_id' => $this->subject->id,
                'date_from' => today()->format('Y-m-d'),
                'date_to' => today()->format('Y-m-d'),
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'summary' => [
                        'total_students',
                        'present_count',
                        'absent_count',
                        'late_count',
                        'excused_count',
                        'attendance_rate',
                    ],
                    'trends',
                    'daily_breakdown',
                    'student_performance',
                ]);
    }

    public function test_export_report_generates_file()
    {
        // Create some attendance data
        Attendance::factory()->count(3)->create([
            'teacher_id' => $this->teacher->id,
            'subject_id' => $this->subject->id,
            'date' => today(),
            'status' => AttendanceStatus::PRESENT,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('attendance.export'), [
                'export_type' => 'attendance',
                'format' => 'csv',
                'subject_id' => $this->subject->id,
                'date_from' => today()->format('Y-m-d'),
                'date_to' => today()->format('Y-m-d'),
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Export completed successfully',
                ])
                ->assertJsonStructure([
                    'download_url',
                    'filename',
                ]);
    }
}
