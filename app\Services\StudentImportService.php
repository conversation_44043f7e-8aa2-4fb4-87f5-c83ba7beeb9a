<?php

namespace App\Services;

use App\Models\Student;
use App\Enums\StudentStatus;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class StudentImportService
{
    /**
     * Import students from CSV/Excel file.
     */
    public function import(UploadedFile $file): array
    {
        $results = [
            'total' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => [],
            'students' => [],
        ];

        try {
            DB::beginTransaction();

            $import = new StudentImport();
            Excel::import($import, $file);

            $results = $import->getResults();

            DB::commit();

            return $results;
        } catch (\Exception $e) {
            DB::rollBack();
            $results['errors'][] = 'Import failed: ' . $e->getMessage();
            return $results;
        }
    }

    /**
     * Validate CSV/Excel file structure.
     */
    public function validateFile(UploadedFile $file): array
    {
        $requiredHeaders = [
            'student_id',
            'first_name',
            'last_name',
            'grade_level',
            'section',
            'parent_phone',
        ];

        try {
            $data = Excel::toArray(new StudentImport(), $file);
            
            if (empty($data) || empty($data[0])) {
                return ['valid' => false, 'error' => 'File is empty or invalid'];
            }

            $headers = array_keys($data[0][0] ?? []);
            $missingHeaders = array_diff($requiredHeaders, $headers);

            if (!empty($missingHeaders)) {
                return [
                    'valid' => false,
                    'error' => 'Missing required headers: ' . implode(', ', $missingHeaders),
                ];
            }

            return [
                'valid' => true,
                'headers' => $headers,
                'row_count' => count($data[0]) - 1, // Exclude header row
            ];
        } catch (\Exception $e) {
            return ['valid' => false, 'error' => 'Failed to read file: ' . $e->getMessage()];
        }
    }

    /**
     * Get sample CSV template.
     */
    public function getSampleTemplate(): array
    {
        return [
            'student_id' => 'SAMPLE001',
            'first_name' => 'Juan',
            'last_name' => 'Dela Cruz',
            'middle_name' => 'Santos',
            'grade_level' => '11',
            'section' => 'A',
            'parent_phone' => '09123456789',
            'emergency_contact' => '09987654321',
            'status' => 'active',
        ];
    }
}

class StudentImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading, WithValidation
{
    private array $results = [
        'total' => 0,
        'successful' => 0,
        'failed' => 0,
        'errors' => [],
        'students' => [],
    ];

    public function model(array $row)
    {
        $this->results['total']++;

        try {
            // Validate row data
            $validator = Validator::make($row, [
                'student_id' => ['required', 'string', 'max:50', 'unique:students,student_id'],
                'first_name' => ['required', 'string', 'max:100'],
                'last_name' => ['required', 'string', 'max:100'],
                'middle_name' => ['nullable', 'string', 'max:100'],
                'grade_level' => ['required', 'in:11,12'],
                'section' => ['required', 'string', 'max:50'],
                'parent_phone' => ['required', 'string', 'max:15'],
                'emergency_contact' => ['nullable', 'string', 'max:15'],
                'status' => ['nullable', 'in:' . implode(',', StudentStatus::values())],
            ]);

            if ($validator->fails()) {
                $this->results['failed']++;
                $this->results['errors'][] = [
                    'row' => $this->results['total'],
                    'student_id' => $row['student_id'] ?? 'N/A',
                    'errors' => $validator->errors()->all(),
                ];
                return null;
            }

            $student = new Student([
                'student_id' => $row['student_id'],
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'middle_name' => $row['middle_name'] ?? null,
                'grade_level' => $row['grade_level'],
                'section' => $row['section'],
                'parent_phone' => $row['parent_phone'],
                'emergency_contact' => $row['emergency_contact'] ?? null,
                'status' => $row['status'] ?? StudentStatus::ACTIVE->value,
            ]);

            $this->results['successful']++;
            $this->results['students'][] = [
                'student_id' => $student->student_id,
                'name' => $student->full_name,
                'grade_level' => $student->grade_level,
                'section' => $student->section,
            ];

            return $student;
        } catch (\Exception $e) {
            $this->results['failed']++;
            $this->results['errors'][] = [
                'row' => $this->results['total'],
                'student_id' => $row['student_id'] ?? 'N/A',
                'errors' => [$e->getMessage()],
            ];
            return null;
        }
    }

    public function batchSize(): int
    {
        return 100;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function rules(): array
    {
        return Student::validationRules();
    }

    public function getResults(): array
    {
        return $this->results;
    }
}
