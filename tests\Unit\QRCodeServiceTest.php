<?php

namespace Tests\Unit;

use App\Models\Student;
use App\Services\QRCodeService;
use App\Enums\StudentStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class QRCodeServiceTest extends TestCase
{
    use RefreshDatabase;

    private QRCodeService $qrCodeService;
    private Student $student;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->qrCodeService = new QRCodeService();
        
        // Create a test student
        $this->student = Student::factory()->create([
            'student_id' => 'TEST001',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'grade_level' => '11',
            'section' => 'A',
            'status' => StudentStatus::ACTIVE,
        ]);

        // Mock storage
        Storage::fake('public');
    }

    public function test_generate_student_qr_creates_qr_code()
    {
        $filename = $this->qrCodeService->generateStudentQR($this->student);

        $this->assertNotNull($filename);
        $this->assertStringContainsString('qr-codes/student-', $filename);
        Storage::disk('public')->assertExists($filename);
        
        // Check that student record was updated
        $this->student->refresh();
        $this->assertEquals($filename, $this->student->qr_code_path);
    }

    public function test_validate_qr_code_with_valid_data()
    {
        // Generate QR code first
        $filename = $this->qrCodeService->generateStudentQR($this->student);
        
        // Get the encrypted payload from the generated QR code
        // For testing, we'll create a valid payload manually
        $payload = [
            'student_id' => $this->student->student_id,
            'name' => $this->student->full_name,
            'grade_level' => $this->student->grade_level,
            'section' => $this->student->section,
            'timestamp' => now()->toISOString(),
            'nonce' => 'test-nonce',
            'hash' => $this->student->qr_code_hash,
        ];
        
        // Add integrity hash
        $hashData = implode('|', [
            $payload['student_id'],
            $payload['name'],
            $payload['grade_level'],
            $payload['section'],
            $payload['timestamp'],
            $payload['nonce'],
            $this->student->qr_code_hash,
            config('app.key'),
        ]);
        $payload['integrity'] = hash_hmac('sha256', $hashData, config('app.key'));
        
        $encryptedPayload = Crypt::encryptString(json_encode($payload));
        
        $result = $this->qrCodeService->validateQRCode($encryptedPayload);
        
        $this->assertTrue($result['valid']);
        $this->assertEquals($this->student->id, $result['student']->id);
        $this->assertEquals($this->student->student_id, $result['data']['student_id']);
    }

    public function test_validate_qr_code_with_invalid_data()
    {
        $result = $this->qrCodeService->validateQRCode('invalid-data');
        
        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('error', $result);
    }

    public function test_batch_generate_qr_processes_multiple_students()
    {
        $students = Student::factory()->count(3)->create([
            'grade_level' => '11',
            'section' => 'A',
            'status' => StudentStatus::ACTIVE,
        ]);
        
        $studentIds = $students->pluck('id')->toArray();
        $result = $this->qrCodeService->batchGenerateQR($studentIds);
        
        $this->assertArrayHasKey('results', $result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertEquals(3, $result['summary']['total']);
        $this->assertEquals(3, $result['summary']['success']);
        $this->assertEquals(0, $result['summary']['errors']);
        
        // Check that all QR codes were created
        foreach ($result['results'] as $qrResult) {
            $this->assertEquals('success', $qrResult['status']);
            Storage::disk('public')->assertExists($qrResult['qr_code_path']);
        }
    }

    public function test_regenerate_qr_creates_new_qr_code()
    {
        // Generate initial QR code
        $originalFilename = $this->qrCodeService->generateStudentQR($this->student);
        
        // Wait a moment to ensure different timestamp
        sleep(1);
        
        // Regenerate QR code
        $newFilename = $this->qrCodeService->regenerateQR($this->student);
        
        $this->assertNotEquals($originalFilename, $newFilename);
        Storage::disk('public')->assertExists($newFilename);
        Storage::disk('public')->assertMissing($originalFilename);
        
        // Check that student record was updated
        $this->student->refresh();
        $this->assertEquals($newFilename, $this->student->qr_code_path);
    }

    public function test_get_qr_data_extracts_student_information()
    {
        // Generate QR code first
        $this->qrCodeService->generateStudentQR($this->student);
        
        // Create valid encrypted payload for testing
        $payload = [
            'student_id' => $this->student->student_id,
            'name' => $this->student->full_name,
            'grade_level' => $this->student->grade_level,
            'section' => $this->student->section,
            'timestamp' => now()->toISOString(),
            'nonce' => 'test-nonce',
            'hash' => $this->student->qr_code_hash,
        ];
        
        // Add integrity hash
        $hashData = implode('|', [
            $payload['student_id'],
            $payload['name'],
            $payload['grade_level'],
            $payload['section'],
            $payload['timestamp'],
            $payload['nonce'],
            $this->student->qr_code_hash,
            config('app.key'),
        ]);
        $payload['integrity'] = hash_hmac('sha256', $hashData, config('app.key'));
        
        $encryptedPayload = Crypt::encryptString(json_encode($payload));
        
        $data = $this->qrCodeService->getQRData($encryptedPayload);
        
        $this->assertNotNull($data);
        $this->assertEquals($this->student->student_id, $data['student_id']);
        $this->assertEquals($this->student->full_name, $data['name']);
        $this->assertEquals($this->student->grade_level, $data['grade_level']);
        $this->assertEquals($this->student->section, $data['section']);
        $this->assertArrayHasKey('generated_at', $data);
        $this->assertArrayHasKey('expires_at', $data);
    }

    public function test_get_qr_code_stats_returns_statistics()
    {
        // Create some students with QR codes
        Student::factory()->count(5)->create([
            'qr_code_path' => 'qr-codes/test.png',
            'status' => StudentStatus::ACTIVE,
        ]);
        
        // Create some students without QR codes
        Student::factory()->count(3)->create([
            'qr_code_path' => null,
            'status' => StudentStatus::ACTIVE,
        ]);
        
        $stats = $this->qrCodeService->getQRCodeStats();
        
        $this->assertArrayHasKey('total_students', $stats);
        $this->assertArrayHasKey('students_with_qr', $stats);
        $this->assertArrayHasKey('coverage_percentage', $stats);
        $this->assertArrayHasKey('recently_generated', $stats);
        $this->assertArrayHasKey('qr_expiration_minutes', $stats);
        $this->assertArrayHasKey('max_batch_size', $stats);
        
        // Including the student created in setUp
        $this->assertEquals(9, $stats['total_students']);
        $this->assertEquals(5, $stats['students_with_qr']);
    }

    public function test_batch_size_limit_is_enforced()
    {
        // Create more students than the batch limit
        $students = Student::factory()->count(150)->create();
        $studentIds = $students->pluck('id')->toArray();
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Batch size exceeds maximum limit');
        
        $this->qrCodeService->batchGenerateQR($studentIds);
    }

    public function test_cleanup_expired_qr_codes()
    {
        // Create some test files in storage
        Storage::disk('public')->put('qr-codes/old-file.png', 'test content');
        Storage::disk('public')->put('qr-codes/new-file.png', 'test content');
        
        // Mock file modification times (this is simplified for testing)
        $result = $this->qrCodeService->cleanupExpiredQRCodes(30);
        
        $this->assertArrayHasKey('deleted_count', $result);
        $this->assertArrayHasKey('error_count', $result);
        $this->assertArrayHasKey('cutoff_date', $result);
    }
}
