<?php

namespace Tests\Unit\Listeners;

use App\Enums\AttendanceStatus;
use App\Events\AttendanceRecorded;
use App\Listeners\SendAttendanceSmsNotification;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Teacher;
use App\Services\TextBeeSmsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class SendAttendanceSmsNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected $smsService;
    protected $listener;
    protected $teacher;
    protected $student;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->smsService = Mockery::mock(TextBeeSmsService::class);
        $this->listener = new SendAttendanceSmsNotification($this->smsService);
        
        $this->teacher = Teacher::factory()->create();
        $this->student = Student::factory()->create([
            'parent_phone' => '+639123456789',
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_sends_sms_for_absent_status()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::ABSENT,
        ]);

        $this->smsService
            ->shouldReceive('sendAttendanceAlert')
            ->once()
            ->with($attendance)
            ->andReturn([
                'success' => true,
                'message_id' => 'msg_123',
                'cost' => 1.50,
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Attendance SMS sent successfully', Mockery::type('array'));

        $event = new AttendanceRecorded($attendance);
        $this->listener->handle($event);
    }

    /** @test */
    public function it_sends_sms_for_late_status()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::LATE,
        ]);

        $this->smsService
            ->shouldReceive('sendAttendanceAlert')
            ->once()
            ->with($attendance)
            ->andReturn([
                'success' => true,
                'message_id' => 'msg_124',
                'cost' => 1.50,
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Attendance SMS sent successfully', Mockery::type('array'));

        $event = new AttendanceRecorded($attendance);
        $this->listener->handle($event);
    }

    /** @test */
    public function it_sends_sms_for_present_status()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::PRESENT,
        ]);

        $this->smsService
            ->shouldReceive('sendAttendanceAlert')
            ->once()
            ->with($attendance)
            ->andReturn([
                'success' => true,
                'message_id' => 'msg_125',
                'cost' => 1.50,
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Attendance SMS sent successfully', Mockery::type('array'));

        $event = new AttendanceRecorded($attendance);
        $this->listener->handle($event);
    }

    /** @test */
    public function it_does_not_send_sms_for_excused_status()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::EXCUSED,
        ]);

        $this->smsService
            ->shouldNotReceive('sendAttendanceAlert');

        $event = new AttendanceRecorded($attendance);
        $this->listener->handle($event);
    }

    /** @test */
    public function it_does_not_send_sms_when_no_parent_phone()
    {
        $studentWithoutPhone = Student::factory()->create([
            'parent_phone' => null,
        ]);

        $attendance = Attendance::factory()->create([
            'student_id' => $studentWithoutPhone->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::ABSENT,
        ]);

        $this->smsService
            ->shouldNotReceive('sendAttendanceAlert');

        $event = new AttendanceRecorded($attendance);
        $this->listener->handle($event);
    }

    /** @test */
    public function it_logs_warning_when_sms_fails()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::ABSENT,
        ]);

        $this->smsService
            ->shouldReceive('sendAttendanceAlert')
            ->once()
            ->with($attendance)
            ->andReturn([
                'success' => false,
                'error' => 'Network error',
            ]);

        Log::shouldReceive('warning')
            ->once()
            ->with('Failed to send attendance SMS', Mockery::type('array'));

        $event = new AttendanceRecorded($attendance);
        $this->listener->handle($event);
    }

    /** @test */
    public function it_handles_exceptions_gracefully()
    {
        $attendance = Attendance::factory()->create([
            'student_id' => $this->student->id,
            'teacher_id' => $this->teacher->id,
            'status' => AttendanceStatus::ABSENT,
        ]);

        $this->smsService
            ->shouldReceive('sendAttendanceAlert')
            ->once()
            ->with($attendance)
            ->andThrow(new \Exception('Service unavailable'));

        Log::shouldReceive('error')
            ->once()
            ->with('Exception sending attendance SMS', Mockery::type('array'));

        $event = new AttendanceRecorded($attendance);
        
        // Should not throw exception
        $this->listener->handle($event);
    }

    /** @test */
    public function it_determines_correct_statuses_for_sms()
    {
        $shouldSendStatuses = [
            AttendanceStatus::ABSENT,
            AttendanceStatus::LATE,
            AttendanceStatus::PRESENT,
        ];

        $shouldNotSendStatuses = [
            AttendanceStatus::EXCUSED,
        ];

        foreach ($shouldSendStatuses as $status) {
            $attendance = Attendance::factory()->make(['status' => $status]);
            $this->assertTrue(
                $this->callProtectedMethod($this->listener, 'shouldSendSms', [$attendance]),
                "Should send SMS for status: {$status->value}"
            );
        }

        foreach ($shouldNotSendStatuses as $status) {
            $attendance = Attendance::factory()->make(['status' => $status]);
            $this->assertFalse(
                $this->callProtectedMethod($this->listener, 'shouldSendSms', [$attendance]),
                "Should not send SMS for status: {$status->value}"
            );
        }
    }

    /**
     * Call protected method for testing.
     */
    protected function callProtectedMethod($object, $method, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}
