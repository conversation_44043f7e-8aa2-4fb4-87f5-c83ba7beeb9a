<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_logs', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Message identification
            $table->string('message_id')->unique()->nullable()->comment('Provider message ID');
            $table->string('batch_id')->nullable()->comment('Batch ID for bulk messages');
            
            // Sender information
            $table->foreignId('teacher_id')
                  ->nullable()
                  ->constrained('teachers')
                  ->onDelete('set null')
                  ->comment('Teacher who sent the message');
            
            // Recipient information
            $table->string('recipient_phone', 20)->comment('Recipient phone number');
            $table->string('recipient_name')->nullable()->comment('Recipient name');
            $table->string('recipient_type')->default('parent')->comment('parent, student, teacher, admin');
            
            // Message content
            $table->text('message_content')->comment('SMS message content');
            $table->string('message_type')->default('manual')->comment('attendance, bulk, manual, alert');
            $table->string('template_used')->nullable()->comment('Template name if used');
            
            // Provider and delivery information
            $table->string('provider')->default('textbee')->comment('SMS provider used');
            $table->enum('status', ['pending', 'queued', 'sent', 'delivered', 'failed', 'expired'])
                  ->default('pending')
                  ->comment('Message delivery status');
            $table->text('provider_response')->nullable()->comment('Provider API response');
            $table->string('failure_reason')->nullable()->comment('Reason for failure');
            
            // Cost and usage tracking
            $table->decimal('cost', 8, 4)->default(0)->comment('Cost per SMS in PHP');
            $table->integer('message_parts')->default(1)->comment('Number of SMS parts');
            $table->integer('character_count')->comment('Message character count');
            
            // Timing information
            $table->timestamp('queued_at')->nullable()->comment('When message was queued');
            $table->timestamp('sent_at')->nullable()->comment('When message was sent to provider');
            $table->timestamp('delivered_at')->nullable()->comment('When message was delivered');
            $table->timestamp('failed_at')->nullable()->comment('When message failed');
            
            // Retry information
            $table->integer('retry_count')->default(0)->comment('Number of retry attempts');
            $table->timestamp('next_retry_at')->nullable()->comment('Next retry attempt time');
            $table->integer('max_retries')->default(3)->comment('Maximum retry attempts');
            
            // Metadata
            $table->json('metadata')->nullable()->comment('Additional message metadata');
            $table->string('ip_address')->nullable()->comment('IP address of sender');
            $table->text('user_agent')->nullable()->comment('User agent of sender');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['teacher_id', 'created_at']); // Teacher SMS history
            $table->index(['recipient_phone', 'created_at']); // Recipient SMS history
            $table->index(['status', 'created_at']); // Status filtering
            $table->index(['message_type', 'created_at']); // Message type filtering
            $table->index(['provider', 'created_at']); // Provider filtering
            $table->index(['batch_id']); // Batch message queries
            $table->index(['queued_at', 'status']); // Queue processing
            $table->index(['next_retry_at', 'retry_count']); // Retry processing
            $table->index(['created_at']); // Date-based queries
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_logs');
    }
};
