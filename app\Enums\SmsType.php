<?php

namespace App\Enums;

enum SmsType: string
{
    case ATTENDANCE = 'attendance';
    case BULK = 'bulk';
    case MANUAL = 'manual';
    case ALERT = 'alert';
    case ANNOUNCEMENT = 'announcement';
    case REMINDER = 'reminder';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match($this) {
            self::ATTENDANCE => 'Attendance Notification',
            self::BULK => 'Bulk Message',
            self::MANUAL => 'Manual Message',
            self::ALERT => 'Alert Message',
            self::ANNOUNCEMENT => 'Announcement',
            self::REMINDER => 'Reminder',
        };
    }

    /**
     * Get type icon for UI.
     */
    public function icon(): string
    {
        return match($this) {
            self::ATTENDANCE => 'calendar-check',
            self::BULK => 'users',
            self::MANUAL => 'edit',
            self::ALERT => 'alert-triangle',
            self::ANNOUNCEMENT => 'megaphone',
            self::REMINDER => 'bell',
        };
    }

    /**
     * Get type color for UI.
     */
    public function color(): string
    {
        return match($this) {
            self::ATTENDANCE => 'blue',
            self::BULK => 'purple',
            self::MANUAL => 'gray',
            self::ALERT => 'red',
            self::ANNOUNCEMENT => 'green',
            self::REMINDER => 'yellow',
        };
    }

    /**
     * Check if type requires template.
     */
    public function requiresTemplate(): bool
    {
        return in_array($this, [self::ATTENDANCE, self::BULK, self::ANNOUNCEMENT, self::REMINDER]);
    }

    /**
     * Check if type supports scheduling.
     */
    public function supportsScheduling(): bool
    {
        return in_array($this, [self::BULK, self::ANNOUNCEMENT, self::REMINDER]);
    }
}
