# QR Code Service for Laravel

A comprehensive QR Code service class for <PERSON><PERSON> with advanced security features, batch processing, and PDF generation capabilities.

## Features

- **🔐 AES Encryption**: Student data is encrypted using <PERSON><PERSON>'s built-in encryption
- **⏰ Time-based Validation**: QR codes expire after a configurable time period
- **🛡️ Tamper-proof Design**: Integrity checks prevent QR code manipulation
- **📦 Batch Processing**: Generate QR codes for multiple students efficiently
- **📄 PDF Generation**: Create printable PDF files with QR codes
- **🔧 Error Handling**: Comprehensive error handling and logging
- **📊 Statistics**: Track QR code usage and coverage
- **🧹 Cleanup**: Automated cleanup of expired QR code files

## Installation

The service uses the existing `endroid/qr-code` package and adds `barryvdh/laravel-dompdf` for PDF generation.

### Required Packages

```bash
composer require barryvdh/laravel-dompdf
```

### Database Migration

Run the migration to add the `qr_code_path` column to the students table:

```bash
php artisan migrate
```

## Configuration

The service uses a configuration file at `config/qrcode.php`. You can customize various settings:

```php
// config/qrcode.php
return [
    'expiration_minutes' => env('QR_EXPIRATION_MINUTES', 60),
    'max_batch_size' => env('QR_MAX_BATCH_SIZE', 100),
    'defaults' => [
        'size' => env('QR_DEFAULT_SIZE', 300),
        'margin' => env('QR_DEFAULT_MARGIN', 10),
        'show_label' => env('QR_SHOW_LABEL', true),
    ],
    // ... more configuration options
];
```

### Environment Variables

Add these to your `.env` file:

```env
QR_EXPIRATION_MINUTES=60
QR_MAX_BATCH_SIZE=100
QR_DEFAULT_SIZE=300
QR_DEFAULT_MARGIN=10
QR_SHOW_LABEL=true
QR_ENCRYPTION_ENABLED=true
QR_INTEGRITY_CHECK=true
QR_TIME_VALIDATION=true
```

## Usage

### Basic Usage

```php
use App\Services\QRCodeService;
use App\Models\Student;

$qrService = new QRCodeService();
$student = Student::find(1);

// Generate QR code for a student
$filename = $qrService->generateStudentQR($student);

// Validate a scanned QR code
$result = $qrService->validateQRCode($qrData);
if ($result['valid']) {
    $student = $result['student'];
    // Process valid scan
}

// Extract data from QR code
$data = $qrService->getQRData($qrData);
```

### Batch Processing

```php
// Generate QR codes for multiple students
$studentIds = [1, 2, 3, 4, 5];
$result = $qrService->batchGenerateQR($studentIds);

// Check results
foreach ($result['results'] as $qrResult) {
    if ($qrResult['status'] === 'success') {
        echo "QR code generated for {$qrResult['name']}: {$qrResult['qr_code_path']}\n";
    } else {
        echo "Failed for {$qrResult['name']}: {$qrResult['error']}\n";
    }
}
```

### PDF Generation

```php
// Generate PDF with QR codes
$studentIds = [1, 2, 3, 4, 5];
$pdfPath = $qrService->generateQRCodesPDF($studentIds);

// The PDF will be saved to storage/app/public/qr-codes/
```

### QR Code Regeneration

```php
// Regenerate QR code for a student (creates new encrypted data)
$newFilename = $qrService->regenerateQR($student);
```

## API Methods

### Core Methods

#### `generateStudentQR(Student $student, array $options = []): string`
Generate a unique QR code for a student with encryption.

**Parameters:**
- `$student`: Student model instance
- `$options`: Optional array with `size`, `margin`, `show_label`, `logo_path`

**Returns:** Path to generated QR code file

#### `validateQRCode(string $qrData): array`
Decrypt and validate scanned QR code.

**Parameters:**
- `$qrData`: Raw QR code data (encrypted)

**Returns:** Array with validation result:
```php
[
    'valid' => true|false,
    'student' => Student|null,
    'data' => array|null,
    'error' => string|null,
    'scanned_at' => Carbon|null
]
```

#### `batchGenerateQR(array $studentIds, array $options = []): array`
Generate QR codes for multiple students.

**Parameters:**
- `$studentIds`: Array of student IDs
- `$options`: Optional generation options

**Returns:** Array with batch results and summary

#### `regenerateQR(Student $student, array $options = []): string`
Create new QR code for existing student.

#### `getQRData(string $qrData): ?array`
Extract student data from QR code without full validation.

### Utility Methods

#### `getQRCodeStats(): array`
Get statistics about QR code usage.

#### `cleanupExpiredQRCodes(int $daysOld = 30): array`
Clean up old QR code files.

#### `generateQRCodesPDF(array $studentIds, array $options = []): string`
Generate PDF with QR codes for printing.

## Command Line Interface

Use the Artisan command for QR code management:

```bash
# Generate QR codes for all students
php artisan qr:manage generate

# Generate for specific grade/section
php artisan qr:manage generate --grade=11 --section=A

# Generate for specific student
php artisan qr:manage generate --student-id=STU001

# Regenerate QR code for a student
php artisan qr:manage regenerate --student-id=STU001

# Get statistics
php artisan qr:manage stats

# Cleanup old files
php artisan qr:manage cleanup --days=30

# Force cleanup without confirmation
php artisan qr:manage cleanup --days=30 --force
```

## Security Features

### Encryption
All QR codes contain encrypted student data using Laravel's encryption system.

### Time-based Validation
QR codes expire after a configurable time period (default: 60 minutes).

### Integrity Checks
Each QR code includes an HMAC signature to prevent tampering.

### Secure Payload Structure
```php
[
    'student_id' => 'STU001',
    'name' => 'John Doe',
    'grade_level' => '11',
    'section' => 'A',
    'timestamp' => '2024-01-01T12:00:00Z',
    'nonce' => 'random-string',
    'hash' => 'student-hash',
    'integrity' => 'hmac-signature'
]
```

## Error Handling

The service includes comprehensive error handling and logging:

```php
try {
    $filename = $qrService->generateStudentQR($student);
} catch (\Exception $e) {
    Log::error('QR generation failed', [
        'student_id' => $student->student_id,
        'error' => $e->getMessage()
    ]);
}
```

## Testing

Run the test suite:

```bash
php artisan test tests/Unit/QRCodeServiceTest.php
```

The test suite covers:
- QR code generation
- Validation with valid/invalid data
- Batch processing
- Data extraction
- Statistics
- Error scenarios

## File Structure

```
app/
├── Services/
│   └── QRCodeService.php          # Main service class
├── Http/Controllers/
│   └── QRCodeController.php       # API controller
├── Console/Commands/
│   └── QRCodeManagement.php       # CLI command
config/
└── qrcode.php                     # Configuration file
resources/views/
└── qr-codes/
    └── pdf.blade.php              # PDF template
tests/Unit/
└── QRCodeServiceTest.php          # Test suite
```

## Performance Considerations

- Batch processing is limited to 100 students by default
- QR code files are stored in `storage/app/public/qr-codes/`
- Old files are automatically cleaned up
- Database queries are optimized with proper indexing

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure storage directory is writable
2. **Memory issues**: Reduce batch size for large operations
3. **Encryption errors**: Check APP_KEY is set correctly
4. **PDF generation fails**: Ensure DomPDF is properly installed

### Logging

All operations are logged. Check `storage/logs/laravel.log` for details.

## License

This QR Code service is part of your Laravel application and follows the same license terms.
