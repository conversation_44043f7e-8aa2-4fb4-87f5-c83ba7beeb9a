<?php

namespace App\Services;

use App\Enums\SmsProvider;
use App\Enums\SmsStatus;
use App\Enums\SmsType;
use App\Enums\QueueStatus;
use App\Models\SmsLog;
use App\Models\SmsQueue;
use App\Models\SmsTemplate;
use App\Models\SmsUsageTracking;
use App\Models\SmsRateLimit;
use App\Models\Attendance;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TextBeeSmsService
{
    protected array $config;
    protected SmsProvider $provider;

    public function __construct()
    {
        $this->config = config('services.sms');
        $this->provider = SmsProvider::TEXTBEE;
    }

    /**
     * Send attendance alert to parents.
     */
    public function sendAttendanceAlert(Attendance $attendance, ?string $templateName = null): array
    {
        $student = $attendance->student;
        $teacher = $attendance->teacher;
        $subject = $attendance->subject;

        if (!$student->parent_phone) {
            return [
                'success' => false,
                'error' => 'No parent phone number available',
                'message_id' => null,
            ];
        }

        // Get or create template
        $template = $this->getAttendanceTemplate($templateName);
        
        // Prepare template variables
        $variables = [
            'student_name' => $student->full_name,
            'parent_name' => $student->parent_name ?? 'Parent/Guardian',
            'date' => $attendance->date instanceof Carbon ? $attendance->date->format('M d, Y') : Carbon::parse($attendance->date)->format('M d, Y'),
            'time' => $attendance->time_in ? ($attendance->time_in instanceof Carbon ? $attendance->time_in->format('h:i A') : Carbon::parse($attendance->time_in)->format('h:i A')) : 'N/A',
            'subject' => $subject->name ?? 'N/A',
            'teacher_name' => $teacher->full_name,
            'school_name' => config('app.school_name', 'School'),
            'status' => $attendance->status->label(),
        ];

        // Format message
        $message = $this->formatMessage($template->content, $variables);

        // Send SMS
        return $this->sendSMS(
            phone: $student->formatted_phone,
            message: $message,
            type: SmsType::ATTENDANCE,
            teacherId: $teacher->id,
            recipientName: $student->full_name,
            recipientType: 'parent',
            templateUsed: $template->name,
            metadata: [
                'student_id' => $student->id,
                'attendance_id' => $attendance->id,
                'subject_id' => $subject->id ?? null,
            ]
        );
    }

    /**
     * Send bulk SMS to multiple recipients.
     */
    public function sendBulkSMS(
        array $recipients,
        string $message,
        ?int $teacherId = null,
        ?string $templateName = null,
        array $options = []
    ): array {
        $batchId = Str::uuid()->toString();
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        // Check rate limits
        if (!$this->checkRateLimit($teacherId, count($recipients))) {
            return [
                'success' => false,
                'error' => 'Rate limit exceeded',
                'batch_id' => $batchId,
                'results' => [],
                'summary' => [
                    'total' => count($recipients),
                    'success' => 0,
                    'failed' => count($recipients),
                ],
            ];
        }

        foreach ($recipients as $recipient) {
            $phone = $recipient['phone'] ?? $recipient;
            $name = $recipient['name'] ?? null;
            $variables = $recipient['variables'] ?? [];

            // Format message with variables if provided
            $formattedMessage = empty($variables) ? $message : $this->formatMessage($message, $variables);

            // Validate phone number
            if (!$this->validatePhone($phone)) {
                $results[] = [
                    'phone' => $phone,
                    'name' => $name,
                    'success' => false,
                    'error' => 'Invalid phone number format',
                    'message_id' => null,
                ];
                $failureCount++;
                continue;
            }

            // Queue or send immediately based on options
            if ($options['queue'] ?? true) {
                $queueResult = $this->queueSMS(
                    phone: $phone,
                    message: $formattedMessage,
                    type: SmsType::BULK,
                    teacherId: $teacherId,
                    recipientName: $name,
                    batchId: $batchId,
                    templateUsed: $templateName,
                    scheduledAt: $options['scheduled_at'] ?? null,
                    priority: $options['priority'] ?? 5
                );

                $results[] = [
                    'phone' => $phone,
                    'name' => $name,
                    'success' => $queueResult['success'],
                    'error' => $queueResult['error'] ?? null,
                    'queue_id' => $queueResult['queue_id'] ?? null,
                ];

                if ($queueResult['success']) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            } else {
                $sendResult = $this->sendSMS(
                    phone: $phone,
                    message: $formattedMessage,
                    type: SmsType::BULK,
                    teacherId: $teacherId,
                    recipientName: $name,
                    batchId: $batchId,
                    templateUsed: $templateName
                );

                $results[] = [
                    'phone' => $phone,
                    'name' => $name,
                    'success' => $sendResult['success'],
                    'error' => $sendResult['error'] ?? null,
                    'message_id' => $sendResult['message_id'] ?? null,
                ];

                if ($sendResult['success']) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            }
        }

        return [
            'success' => $successCount > 0,
            'batch_id' => $batchId,
            'results' => $results,
            'summary' => [
                'total' => count($recipients),
                'success' => $successCount,
                'failed' => $failureCount,
            ],
        ];
    }

    /**
     * Get delivery status of a message.
     */
    public function getDeliveryStatus(string $messageId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->config['textbee']['api_key'],
                'Content-Type' => 'application/json',
            ])->get($this->config['textbee']['api_url'] . '/sms/status/' . $messageId);

            if ($response->successful()) {
                $data = $response->json();
                
                // Update local SMS log if exists
                $smsLog = SmsLog::where('message_id', $messageId)->first();
                if ($smsLog) {
                    $this->updateSmsLogStatus($smsLog, $data);
                }

                return [
                    'success' => true,
                    'status' => $this->mapProviderStatus($data['status'] ?? 'unknown'),
                    'delivered_at' => $data['delivered_at'] ?? null,
                    'provider_data' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to get delivery status',
                'status' => SmsStatus::FAILED,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get SMS delivery status', [
                'message_id' => $messageId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status' => SmsStatus::FAILED,
            ];
        }
    }

    /**
     * Format SMS message using templates and variables.
     */
    public function formatMessage(string $template, array $variables = []): string
    {
        $message = $template;
        
        foreach ($variables as $key => $value) {
            $placeholder = '{' . $key . '}';
            $message = str_replace($placeholder, $value, $message);
        }
        
        // Clean up any remaining placeholders
        $message = preg_replace('/\{[^}]+\}/', '', $message);
        
        // Clean up extra whitespace
        $message = preg_replace('/\s+/', ' ', trim($message));
        
        return $message;
    }

    /**
     * Validate Philippine phone number format.
     */
    public function validatePhone(string $phone): bool
    {
        // Clean phone number
        $cleanPhone = $this->cleanPhoneNumber($phone);
        
        // Philippine mobile numbers: +639XXXXXXXXX (13 digits total)
        return preg_match('/^\+639\d{9}$/', $cleanPhone) === 1;
    }

    /**
     * Log SMS usage for tracking and reporting.
     */
    public function logSMSUsage(
        int $teacherId,
        string $messageType,
        float $cost,
        bool $delivered = false,
        string $provider = 'textbee'
    ): void {
        $today = now()->toDateString();
        
        // Get or create daily tracking record
        $tracking = SmsUsageTracking::firstOrCreate(
            [
                'teacher_id' => $teacherId,
                'tracking_date' => $today,
                'tracking_period' => 'daily',
            ],
            [
                'messages_sent' => 0,
                'messages_delivered' => 0,
                'messages_failed' => 0,
                'total_cost' => 0,
                'attendance_messages' => 0,
                'bulk_messages' => 0,
                'manual_messages' => 0,
                'alert_messages' => 0,
                'provider_usage' => [],
            ]
        );

        // Update tracking record
        $tracking->addMessage($messageType, $cost, $delivered);

        // Update provider usage
        $providerUsage = $tracking->provider_usage ?? [];
        $providerUsage[$provider] = ($providerUsage[$provider] ?? 0) + 1;
        $tracking->update(['provider_usage' => $providerUsage]);
    }

    /**
     * Configure SMS gateway settings.
     */
    public function configureSMSGateway(array $settings): array
    {
        try {
            // Validate required settings
            $requiredSettings = ['api_key', 'sender_name'];
            foreach ($requiredSettings as $setting) {
                if (empty($settings[$setting])) {
                    return [
                        'success' => false,
                        'error' => "Missing required setting: {$setting}",
                    ];
                }
            }

            // Test connection with new settings
            $testResult = $this->testConnection($settings);
            if (!$testResult['success']) {
                return [
                    'success' => false,
                    'error' => 'Connection test failed: ' . $testResult['error'],
                ];
            }

            // Update configuration (in production, this would update environment variables)
            $this->config['textbee'] = array_merge($this->config['textbee'], $settings);

            return [
                'success' => true,
                'message' => 'SMS gateway configured successfully',
                'test_result' => $testResult,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Configuration failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Queue SMS for batch sending.
     */
    public function queueSMS(
        string $phone,
        string $message,
        SmsType $type = SmsType::MANUAL,
        ?int $teacherId = null,
        ?string $recipientName = null,
        ?string $recipientType = 'parent',
        ?string $batchId = null,
        ?string $templateUsed = null,
        ?\DateTime $scheduledAt = null,
        int $priority = 5,
        array $metadata = []
    ): array {
        try {
            // Validate phone number
            if (!$this->validatePhone($phone)) {
                return [
                    'success' => false,
                    'error' => 'Invalid phone number format',
                    'queue_id' => null,
                ];
            }

            // Create queue entry
            $queueEntry = SmsQueue::create([
                'batch_id' => $batchId,
                'priority' => $priority,
                'teacher_id' => $teacherId,
                'recipient_phone' => $phone,
                'recipient_name' => $recipientName,
                'recipient_type' => $recipientType,
                'message_content' => $message,
                'message_type' => $type,
                'template_used' => $templateUsed,
                'status' => QueueStatus::PENDING,
                'scheduled_at' => $scheduledAt,
                'provider' => $this->provider,
                'provider_options' => $this->config['textbee'],
                'max_retries' => $this->config['textbee']['max_retries'] ?? 3,
                'retry_delay' => $this->config['textbee']['retry_delay'] ?? 300,
                'metadata' => $metadata,
                'ip_address' => request()->ip(),
            ]);

            return [
                'success' => true,
                'queue_id' => $queueEntry->id,
                'estimated_cost' => $queueEntry->getEstimatedCost(),
                'estimated_parts' => $queueEntry->getEstimatedParts(),
            ];
        } catch (\Exception $e) {
            Log::error('Failed to queue SMS', [
                'phone' => $phone,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to queue SMS: ' . $e->getMessage(),
                'queue_id' => null,
            ];
        }
    }

    /**
     * Retry failed SMS messages.
     */
    public function retryFailedSMS(?int $teacherId = null, ?int $maxRetries = null): array
    {
        $query = SmsLog::retryable();

        if ($teacherId) {
            $query->forTeacher($teacherId);
        }

        if ($maxRetries) {
            $query->limit($maxRetries);
        }

        $failedMessages = $query->get();
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($failedMessages as $smsLog) {
            try {
                // Check if we can retry this message
                if (!$smsLog->can_retry) {
                    $results[] = [
                        'sms_log_id' => $smsLog->id,
                        'success' => false,
                        'error' => 'Maximum retries exceeded',
                    ];
                    $failureCount++;
                    continue;
                }

                // Increment retry count
                $smsLog->incrementRetry();

                // Attempt to resend
                $result = $this->sendSMSToProvider(
                    $smsLog->recipient_phone,
                    $smsLog->message_content
                );

                if ($result['success']) {
                    $smsLog->markAsSent($result['message_id'], $result['provider_response']);
                    $successCount++;

                    $results[] = [
                        'sms_log_id' => $smsLog->id,
                        'success' => true,
                        'message_id' => $result['message_id'],
                    ];
                } else {
                    $smsLog->markAsFailed($result['error'], $result['provider_response'] ?? []);
                    $failureCount++;

                    $results[] = [
                        'sms_log_id' => $smsLog->id,
                        'success' => false,
                        'error' => $result['error'],
                    ];
                }
            } catch (\Exception $e) {
                $smsLog->markAsFailed('Retry exception: ' . $e->getMessage());
                $failureCount++;

                $results[] = [
                    'sms_log_id' => $smsLog->id,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return [
            'success' => $successCount > 0,
            'summary' => [
                'total_attempted' => count($failedMessages),
                'success' => $successCount,
                'failed' => $failureCount,
            ],
            'results' => $results,
        ];
    }

    /**
     * Generate SMS usage statistics and reports.
     */
    public function generateSMSReport(
        ?int $teacherId = null,
        ?string $startDate = null,
        ?string $endDate = null,
        string $period = 'daily'
    ): array {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        $query = SmsUsageTracking::query()
            ->inDateRange($startDate, $endDate)
            ->where('tracking_period', $period);

        if ($teacherId) {
            $query->forTeacher($teacherId);
        }

        $trackingData = $query->with('teacher')->get();

        // Calculate summary statistics
        $summary = [
            'total_messages' => $trackingData->sum('messages_sent'),
            'total_delivered' => $trackingData->sum('messages_delivered'),
            'total_failed' => $trackingData->sum('messages_failed'),
            'total_cost' => $trackingData->sum('total_cost'),
            'average_success_rate' => $trackingData->avg('success_rate'),
            'total_rate_limit_hits' => $trackingData->sum('rate_limit_hits'),
        ];

        // Calculate message type breakdown
        $messageTypes = [
            'attendance' => $trackingData->sum('attendance_messages'),
            'bulk' => $trackingData->sum('bulk_messages'),
            'manual' => $trackingData->sum('manual_messages'),
            'alert' => $trackingData->sum('alert_messages'),
        ];

        // Calculate provider usage
        $providerUsage = [];
        foreach ($trackingData as $tracking) {
            $usage = $tracking->provider_usage ?? [];
            foreach ($usage as $provider => $count) {
                $providerUsage[$provider] = ($providerUsage[$provider] ?? 0) + $count;
            }
        }

        // Top users (if not filtering by specific teacher)
        $topUsers = [];
        if (!$teacherId) {
            $topUsers = $trackingData->groupBy('teacher_id')
                ->map(function ($group) {
                    return [
                        'teacher' => $group->first()->teacher,
                        'total_messages' => $group->sum('messages_sent'),
                        'total_cost' => $group->sum('total_cost'),
                        'success_rate' => $group->avg('success_rate'),
                    ];
                })
                ->sortByDesc('total_messages')
                ->take(10)
                ->values();
        }

        // Daily/period breakdown
        $periodBreakdown = $trackingData->groupBy('tracking_date')
            ->map(function ($group, $date) {
                return [
                    'date' => $date,
                    'messages_sent' => $group->sum('messages_sent'),
                    'messages_delivered' => $group->sum('messages_delivered'),
                    'total_cost' => $group->sum('total_cost'),
                    'success_rate' => $group->avg('success_rate'),
                ];
            })
            ->values();

        return [
            'success' => true,
            'period' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
                'type' => $period,
            ],
            'summary' => $summary,
            'message_types' => $messageTypes,
            'provider_usage' => $providerUsage,
            'top_users' => $topUsers,
            'period_breakdown' => $periodBreakdown,
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Send SMS to provider (core sending method).
     */
    protected function sendSMS(
        string $phone,
        string $message,
        SmsType $type = SmsType::MANUAL,
        ?int $teacherId = null,
        ?string $recipientName = null,
        ?string $recipientType = 'parent',
        ?string $batchId = null,
        ?string $templateUsed = null,
        array $metadata = []
    ): array {
        // Validate phone number
        if (!$this->validatePhone($phone)) {
            return [
                'success' => false,
                'error' => 'Invalid phone number format',
                'message_id' => null,
            ];
        }

        // Check rate limits
        if ($teacherId && !$this->checkRateLimit($teacherId)) {
            return [
                'success' => false,
                'error' => 'Rate limit exceeded',
                'message_id' => null,
            ];
        }

        // Calculate cost and parts
        $characterCount = strlen($message);
        $messageParts = $this->calculateMessageParts($characterCount);
        $cost = $messageParts * ($this->config['textbee']['cost_per_sms'] ?? 0.50);

        // Create SMS log entry
        $smsLog = SmsLog::create([
            'batch_id' => $batchId,
            'teacher_id' => $teacherId,
            'recipient_phone' => $phone,
            'recipient_name' => $recipientName,
            'recipient_type' => $recipientType,
            'message_content' => $message,
            'message_type' => $type,
            'template_used' => $templateUsed,
            'provider' => $this->provider,
            'status' => SmsStatus::PENDING,
            'cost' => $cost,
            'message_parts' => $messageParts,
            'character_count' => $characterCount,
            'queued_at' => now(),
            'metadata' => $metadata,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        try {
            // Send to provider
            $result = $this->sendSMSToProvider($phone, $message);

            if ($result['success']) {
                $smsLog->markAsSent($result['message_id'], $result['provider_response']);

                // Log usage
                if ($teacherId) {
                    $this->logSMSUsage($teacherId, $type->value, $cost, false, $this->provider->value);
                }

                return [
                    'success' => true,
                    'message_id' => $result['message_id'],
                    'sms_log_id' => $smsLog->id,
                    'cost' => $cost,
                    'parts' => $messageParts,
                ];
            } else {
                $smsLog->markAsFailed($result['error'], $result['provider_response'] ?? []);

                return [
                    'success' => false,
                    'error' => $result['error'],
                    'sms_log_id' => $smsLog->id,
                ];
            }
        } catch (\Exception $e) {
            $smsLog->markAsFailed('Exception: ' . $e->getMessage());

            Log::error('SMS sending failed', [
                'phone' => $phone,
                'error' => $e->getMessage(),
                'sms_log_id' => $smsLog->id,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'sms_log_id' => $smsLog->id,
            ];
        }
    }

    /**
     * Send SMS to TextBee provider.
     */
    protected function sendSMSToProvider(string $phone, string $message): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->config['textbee']['api_key'],
                'Content-Type' => 'application/json',
            ])->post($this->config['textbee']['api_url'] . '/sms/send', [
                'to' => $phone,
                'message' => $message,
                'sender' => $this->config['textbee']['sender_name'],
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'message_id' => $data['message_id'] ?? null,
                    'provider_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Provider API error: ' . $response->body(),
                'provider_response' => $response->json(),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Connection error: ' . $e->getMessage(),
                'provider_response' => [],
            ];
        }
    }

    /**
     * Test connection to TextBee API.
     */
    protected function testConnection(array $settings = []): array
    {
        $config = array_merge($this->config['textbee'], $settings);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])->get($config['api_url'] . '/account/balance');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'balance' => $response->json()['balance'] ?? 'Unknown',
                ];
            }

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $response->body(),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Connection test failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check rate limits for teacher or global.
     */
    protected function checkRateLimit(?int $teacherId, int $messageCount = 1): bool
    {
        if (!$this->config['enable_rate_limiting']) {
            return true;
        }

        $identifier = $teacherId ? "teacher_{$teacherId}" : 'global';
        $limit = $this->config['textbee']['rate_limit'] ?? 60;

        $rateLimit = SmsRateLimit::firstOrCreate(
            [
                'identifier' => $identifier,
                'type' => $teacherId ? 'teacher' : 'global',
                'limit_period' => 'minute',
            ],
            [
                'limit_count' => $limit,
                'period_start' => now(),
                'period_end' => now()->addMinute(),
                'current_count' => 0,
            ]
        );

        // Reset if period expired
        if (!$rateLimit->isActive()) {
            $rateLimit->resetForNewPeriod();
        }

        // Check if adding messages would exceed limit
        if (($rateLimit->current_count + $messageCount) > $rateLimit->limit_count) {
            // Record rate limit hit
            if ($teacherId) {
                $tracking = SmsUsageTracking::firstOrCreate([
                    'teacher_id' => $teacherId,
                    'tracking_date' => now()->toDateString(),
                    'tracking_period' => 'daily',
                ]);
                $tracking->recordRateLimitHit();
            }

            return false;
        }

        // Increment usage
        for ($i = 0; $i < $messageCount; $i++) {
            $rateLimit->incrementUsage();
        }

        return true;
    }

    /**
     * Clean phone number format.
     */
    protected function cleanPhoneNumber(string $phone): string
    {
        // Remove all non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Convert to international format
        if (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
            // Convert 09XXXXXXXXX to +639XXXXXXXXX
            $phone = '+63' . substr($phone, 1);
        } elseif (strlen($phone) === 10 && substr($phone, 0, 1) === '9') {
            // Convert 9XXXXXXXXX to +639XXXXXXXXX
            $phone = '+63' . $phone;
        } elseif (strlen($phone) === 12 && substr($phone, 0, 2) === '63') {
            // Convert 639XXXXXXXXX to +639XXXXXXXXX
            $phone = '+' . $phone;
        }

        return $phone;
    }

    /**
     * Calculate number of SMS parts based on character count.
     */
    protected function calculateMessageParts(int $characterCount): int
    {
        if ($characterCount <= 160) {
            return 1;
        }

        return ceil($characterCount / 153); // 153 chars per part for multi-part SMS
    }

    /**
     * Get attendance template.
     */
    protected function getAttendanceTemplate(?string $templateName = null): SmsTemplate
    {
        if ($templateName) {
            $template = SmsTemplate::where('name', $templateName)
                ->where('is_active', true)
                ->first();

            if ($template) {
                return $template;
            }
        }

        // Get default attendance template
        $template = SmsTemplate::where('category', 'attendance')
            ->where('is_active', true)
            ->where('is_system', true)
            ->first();

        if (!$template) {
            // Create default template if none exists
            $template = SmsTemplate::create([
                'name' => 'default_attendance',
                'display_name' => 'Default Attendance Alert',
                'content' => 'Dear {parent_name}, your child {student_name} was marked {status} in {subject} on {date} at {time}. - {school_name}',
                'variables' => ['parent_name', 'student_name', 'status', 'subject', 'date', 'time', 'school_name'],
                'category' => 'attendance',
                'type' => 'notification',
                'is_system' => true,
                'is_active' => true,
            ]);
        }

        return $template;
    }

    /**
     * Map provider status to internal status.
     */
    protected function mapProviderStatus(string $providerStatus): SmsStatus
    {
        return match(strtolower($providerStatus)) {
            'pending', 'queued' => SmsStatus::QUEUED,
            'sent', 'sending' => SmsStatus::SENT,
            'delivered' => SmsStatus::DELIVERED,
            'failed', 'error' => SmsStatus::FAILED,
            'expired' => SmsStatus::EXPIRED,
            default => SmsStatus::PENDING,
        };
    }

    /**
     * Update SMS log status from provider data.
     */
    protected function updateSmsLogStatus(SmsLog $smsLog, array $providerData): void
    {
        $status = $this->mapProviderStatus($providerData['status'] ?? 'unknown');

        $updates = [
            'status' => $status,
            'provider_response' => $providerData,
        ];

        if ($status === SmsStatus::DELIVERED && !$smsLog->delivered_at) {
            $updates['delivered_at'] = now();
        } elseif ($status === SmsStatus::FAILED && !$smsLog->failed_at) {
            $updates['failed_at'] = now();
            $updates['failure_reason'] = $providerData['error'] ?? 'Unknown error';
        }

        $smsLog->update($updates);
    }
}
