<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_usage_tracking', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // User identification
            $table->foreignId('teacher_id')
                  ->constrained('teachers')
                  ->onDelete('cascade')
                  ->comment('Teacher using SMS service');
            
            // Time period tracking
            $table->date('tracking_date')->comment('Date of usage tracking');
            $table->string('tracking_period')->default('daily')->comment('daily, weekly, monthly');
            
            // Usage statistics
            $table->integer('messages_sent')->default(0)->comment('Total messages sent');
            $table->integer('messages_delivered')->default(0)->comment('Messages successfully delivered');
            $table->integer('messages_failed')->default(0)->comment('Messages that failed');
            $table->integer('messages_pending')->default(0)->comment('Messages still pending');
            
            // Cost tracking
            $table->decimal('total_cost', 10, 4)->default(0)->comment('Total cost in PHP');
            $table->decimal('average_cost_per_message', 8, 4)->default(0)->comment('Average cost per message');
            
            // Message type breakdown
            $table->integer('attendance_messages')->default(0)->comment('Attendance-related messages');
            $table->integer('bulk_messages')->default(0)->comment('Bulk messages sent');
            $table->integer('manual_messages')->default(0)->comment('Manual messages sent');
            $table->integer('alert_messages')->default(0)->comment('Alert messages sent');
            
            // Provider breakdown
            $table->json('provider_usage')->nullable()->comment('Usage breakdown by provider');
            
            // Rate limiting tracking
            $table->integer('rate_limit_hits')->default(0)->comment('Number of rate limit hits');
            $table->timestamp('last_rate_limit_hit')->nullable()->comment('Last rate limit hit time');
            
            // Performance metrics
            $table->decimal('average_delivery_time', 8, 2)->nullable()->comment('Average delivery time in seconds');
            $table->decimal('success_rate', 5, 2)->default(0)->comment('Success rate percentage');
            
            // Timestamps
            $table->timestamps();
            
            // Unique constraint to prevent duplicate tracking records
            $table->unique(['teacher_id', 'tracking_date', 'tracking_period'], 'unique_usage_tracking');
            
            // Indexes
            $table->index(['teacher_id', 'tracking_date']); // Teacher usage queries
            $table->index(['tracking_date', 'tracking_period']); // Period-based queries
            $table->index(['tracking_date']); // Date-based queries
            $table->index(['total_cost']); // Cost-based queries
            $table->index(['success_rate']); // Performance queries
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_usage_tracking');
    }
};
