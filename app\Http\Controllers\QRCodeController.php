<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Services\QRCodeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class QRCodeController extends Controller
{
    private QRCodeService $qrCodeService;

    public function __construct(QRCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Generate QR code for a specific student
     */
    public function generateForStudent(Request $request, Student $student): JsonResponse
    {
        try {
            $options = $request->only(['size', 'margin', 'show_label', 'logo_path']);
            $filename = $this->qrCodeService->generateStudentQR($student, $options);

            return response()->json([
                'success' => true,
                'message' => 'QR code generated successfully',
                'data' => [
                    'student_id' => $student->student_id,
                    'student_name' => $student->full_name,
                    'qr_code_path' => $filename,
                    'qr_code_url' => Storage::disk('public')->url($filename),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate a scanned QR code
     */
    public function validateQRCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->qrCodeService->validateQRCode($request->qr_data);

            if ($result['valid']) {
                return response()->json([
                    'success' => true,
                    'message' => 'QR code is valid',
                    'data' => [
                        'student' => [
                            'id' => $result['student']->id,
                            'student_id' => $result['student']->student_id,
                            'name' => $result['student']->full_name,
                            'grade_level' => $result['student']->grade_level,
                            'section' => $result['student']->section,
                        ],
                        'scanned_at' => $result['scanned_at'],
                        'qr_data' => $result['data'],
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid QR code',
                    'error' => $result['error']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'QR code validation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate QR codes for multiple students
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_ids' => 'required|array|min:1|max:100',
            'student_ids.*' => 'integer|exists:students,id',
            'options' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $options = $request->get('options', []);
            $result = $this->qrCodeService->batchGenerateQR($request->student_ids, $options);

            return response()->json([
                'success' => true,
                'message' => 'Batch QR generation completed',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Batch QR generation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Regenerate QR code for a student
     */
    public function regenerateForStudent(Request $request, Student $student): JsonResponse
    {
        try {
            $options = $request->only(['size', 'margin', 'show_label', 'logo_path']);
            $filename = $this->qrCodeService->regenerateQR($student, $options);

            return response()->json([
                'success' => true,
                'message' => 'QR code regenerated successfully',
                'data' => [
                    'student_id' => $student->student_id,
                    'student_name' => $student->full_name,
                    'qr_code_path' => $filename,
                    'qr_code_url' => Storage::disk('public')->url($filename),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get QR code statistics
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = $this->qrCodeService->getQRCodeStats();

            return response()->json([
                'success' => true,
                'message' => 'QR code statistics retrieved successfully',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get QR code statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate PDF with QR codes
     */
    public function generatePDF(Request $request): Response
    {
        $validator = Validator::make($request->all(), [
            'student_ids' => 'required|array|min:1|max:100',
            'student_ids.*' => 'integer|exists:students,id',
            'options' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $options = $request->get('options', []);
            $filename = $this->qrCodeService->generateQRCodesPDF($request->student_ids, $options);
            
            $pdfPath = Storage::disk('public')->path($filename);
            
            return response()->download($pdfPath, 'student-qr-codes.pdf', [
                'Content-Type' => 'application/pdf',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate PDF',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extract data from QR code
     */
    public function extractData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $this->qrCodeService->getQRData($request->qr_data);

            if ($data) {
                return response()->json([
                    'success' => true,
                    'message' => 'QR data extracted successfully',
                    'data' => $data
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to extract QR data',
                    'error' => 'Invalid or expired QR code'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'QR data extraction failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
