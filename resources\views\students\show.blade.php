@extends('layouts.app')

@section('title', $student->full_name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
                <a href="{{ route('students.index') }}" class="text-gray-600 hover:text-gray-800 mr-4">
                    ← Back to Students
                </a>
                <h1 class="text-3xl font-bold text-gray-900">{{ $student->full_name }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('students.edit', $student) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                    Edit Student
                </a>
                <button onclick="generateQRCode({{ $student->id }})" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                    Generate QR Code
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Student Information -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Student Information</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Student ID</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->student_id }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Full Name</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->full_name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Grade Level</label>
                            <p class="mt-1 text-sm text-gray-900">Grade {{ $student->grade_level }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Section</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->section }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Parent Phone</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->parent_phone }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Emergency Contact</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->emergency_contact ?? 'N/A' }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Status</label>
                            <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                       {{ $student->status->value === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ $student->status->label() }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Created</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Attendance Statistics -->
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Attendance Statistics</h2>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $attendanceStats['attendance_rate'] }}%</div>
                            <div class="text-sm text-gray-500">Attendance Rate</div>
                        </div>
                        
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ $attendanceStats['present_days'] }}</div>
                            <div class="text-sm text-gray-500">Present Days</div>
                        </div>
                        
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600">{{ $attendanceStats['absent_days'] }}</div>
                            <div class="text-sm text-gray-500">Absent Days</div>
                        </div>
                        
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ $attendanceStats['late_days'] }}</div>
                            <div class="text-sm text-gray-500">Late Days</div>
                        </div>
                    </div>
                    
                    @if($attendanceStats['is_at_risk'])
                        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">At Risk Student</h3>
                                    <p class="text-sm text-red-700">This student has low attendance and may need intervention.</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Recent Attendance -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Attendance (Last 30 Days)</h2>
                    
                    @if($recentAttendance->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time In</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time Out</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Remarks</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($recentAttendance as $attendance)
                                        <tr>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $attendance->date->format('M d, Y') }}</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $attendance->formatted_time_in ?? 'N/A' }}</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $attendance->formatted_time_out ?? 'N/A' }}</td>
                                            <td class="px-4 py-3">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                           {{ $attendance->status->value === 'present' ? 'bg-green-100 text-green-800' : 
                                                              ($attendance->status->value === 'absent' ? 'bg-red-100 text-red-800' : 
                                                               ($attendance->status->value === 'late' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800')) }}">
                                                    {{ $attendance->status->label() }}
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $attendance->remarks ?? 'N/A' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500 text-center py-4">No attendance records found.</p>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Student Photo -->
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Photo</h3>
                    <div class="flex justify-center">
                        @if($student->photo_path)
                            <img class="h-32 w-32 rounded-full object-cover" 
                                 src="{{ Storage::url($student->photo_path) }}" 
                                 alt="{{ $student->full_name }}">
                        @else
                            <div class="h-32 w-32 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-2xl font-medium text-gray-700">
                                    {{ substr($student->first_name, 0, 1) }}{{ substr($student->last_name, 0, 1) }}
                                </span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- QR Code -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">QR Code</h3>
                    <div class="flex justify-center">
                        @if($student->qr_code_path)
                            <img class="h-32 w-32" 
                                 src="{{ Storage::url($student->qr_code_path) }}" 
                                 alt="QR Code for {{ $student->full_name }}">
                        @else
                            <div class="h-32 w-32 bg-gray-100 flex items-center justify-center rounded">
                                <span class="text-sm text-gray-500">No QR Code</span>
                            </div>
                        @endif
                    </div>
                    <div class="mt-4 text-center">
                        <button onclick="generateQRCode({{ $student->id }})" 
                                class="text-sm text-blue-600 hover:text-blue-800">
                            {{ $student->qr_code_path ? 'Regenerate' : 'Generate' }} QR Code
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateQRCode(studentId) {
    if (confirm('Generate QR code for this student?')) {
        fetch('/students/generate-qr-codes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                student_ids: [studentId]
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('QR code generated successfully!');
                location.reload();
            } else {
                alert('Failed to generate QR code: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while generating QR code.');
        });
    }
}
</script>
@endsection
