<?php

namespace App\Services;

use App\Enums\AttendanceStatus;
use App\Enums\StudentStatus;
use App\Models\Attendance;
use App\Models\Student;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class StudentRiskAssessmentService
{
    /**
     * AI-powered student risk assessment.
     */
    public function assessStudentRisk(array $parameters = []): array
    {
        $query = Student::active()->with(['attendance' => function ($q) {
            $q->where('date', '>=', now()->subDays(60))
              ->orderBy('date', 'desc');
        }]);

        // Apply filters
        if (!empty($parameters['grade_level'])) {
            $query->where('grade_level', $parameters['grade_level']);
        }

        if (!empty($parameters['section'])) {
            $query->where('section', $parameters['section']);
        }

        $limit = $parameters['limit'] ?? 50;
        $students = $query->limit($limit * 2)->get(); // Get more to filter

        $riskAssessments = $students->map(function ($student) {
            return $this->assessIndividualStudentRisk($student);
        })->filter(function ($assessment) use ($parameters) {
            $riskLevel = $parameters['risk_level'] ?? 'all';
            return $riskLevel === 'all' || $assessment['risk_level'] === $riskLevel;
        })->take($limit);

        // Sort by risk score (highest first)
        $sortedAssessments = $riskAssessments->sortByDesc('risk_score')->values();

        return [
            'assessments' => $sortedAssessments,
            'summary' => $this->calculateRiskSummary($sortedAssessments),
            'recommendations' => $this->generateRecommendations($sortedAssessments),
            'filters_applied' => $parameters,
        ];
    }

    /**
     * Assess individual student risk.
     */
    protected function assessIndividualStudentRisk(Student $student): array
    {
        $attendanceRecords = $student->attendance;
        $riskFactors = [];
        $riskScore = 0;

        // Factor 1: Overall attendance rate (40% weight)
        $attendanceRate = $this->calculateAttendanceRate($attendanceRecords);
        $attendanceRiskScore = $this->calculateAttendanceRiskScore($attendanceRate);
        $riskScore += $attendanceRiskScore * 0.4;

        if ($attendanceRate < 75) {
            $riskFactors[] = [
                'factor' => 'low_attendance_rate',
                'description' => "Attendance rate is {$attendanceRate}%",
                'severity' => $attendanceRate < 60 ? 'high' : 'medium',
                'weight' => 0.4,
            ];
        }

        // Factor 2: Recent absence pattern (25% weight)
        $recentAbsenceScore = $this->calculateRecentAbsenceScore($attendanceRecords);
        $riskScore += $recentAbsenceScore * 0.25;

        if ($recentAbsenceScore > 50) {
            $recentAbsences = $attendanceRecords->where('date', '>=', now()->subDays(14))
                ->where('status', AttendanceStatus::ABSENT)->count();
            $riskFactors[] = [
                'factor' => 'recent_absences',
                'description' => "{$recentAbsences} absences in the last 2 weeks",
                'severity' => $recentAbsences >= 5 ? 'high' : 'medium',
                'weight' => 0.25,
            ];
        }

        // Factor 3: Consistency pattern (20% weight)
        $consistencyScore = $this->calculateConsistencyScore($attendanceRecords);
        $consistencyRiskScore = 100 - $consistencyScore;
        $riskScore += $consistencyRiskScore * 0.2;

        if ($consistencyScore < 60) {
            $riskFactors[] = [
                'factor' => 'inconsistent_attendance',
                'description' => 'Irregular attendance patterns detected',
                'severity' => $consistencyScore < 40 ? 'high' : 'medium',
                'weight' => 0.2,
            ];
        }

        // Factor 4: Tardiness frequency (10% weight)
        $tardinessScore = $this->calculateTardinessScore($attendanceRecords);
        $riskScore += $tardinessScore * 0.1;

        if ($tardinessScore > 30) {
            $lateCount = $attendanceRecords->where('status', AttendanceStatus::LATE)->count();
            $riskFactors[] = [
                'factor' => 'frequent_tardiness',
                'description' => "{$lateCount} late arrivals recorded",
                'severity' => $tardinessScore > 60 ? 'high' : 'medium',
                'weight' => 0.1,
            ];
        }

        // Factor 5: Declining trend (5% weight)
        $trendScore = $this->calculateTrendScore($attendanceRecords);
        $riskScore += $trendScore * 0.05;

        if ($trendScore > 40) {
            $riskFactors[] = [
                'factor' => 'declining_trend',
                'description' => 'Attendance declining over time',
                'severity' => $trendScore > 70 ? 'high' : 'medium',
                'weight' => 0.05,
            ];
        }

        // Determine risk level
        $riskLevel = $this->determineRiskLevel($riskScore);

        return [
            'student_id' => $student->id,
            'student_number' => $student->student_id,
            'name' => $student->full_name,
            'grade_level' => $student->grade_level,
            'section' => $student->section,
            'risk_score' => round($riskScore, 2),
            'risk_level' => $riskLevel,
            'attendance_rate' => $attendanceRate,
            'risk_factors' => $riskFactors,
            'recommendations' => $this->generateIndividualRecommendations($riskLevel, $riskFactors),
            'last_attendance' => $attendanceRecords->first()?->date,
            'total_absences_60_days' => $attendanceRecords->where('status', AttendanceStatus::ABSENT)->count(),
        ];
    }

    /**
     * Calculate attendance rate.
     */
    protected function calculateAttendanceRate(Collection $attendanceRecords): float
    {
        if ($attendanceRecords->isEmpty()) {
            return 0;
        }

        $totalRecords = $attendanceRecords->count();
        $presentRecords = $attendanceRecords->whereIn('status', [
            AttendanceStatus::PRESENT,
            AttendanceStatus::LATE,
            AttendanceStatus::EXCUSED
        ])->count();

        return round(($presentRecords / $totalRecords) * 100, 2);
    }

    /**
     * Calculate attendance risk score.
     */
    protected function calculateAttendanceRiskScore(float $attendanceRate): float
    {
        if ($attendanceRate >= 95) return 0;
        if ($attendanceRate >= 85) return 20;
        if ($attendanceRate >= 75) return 40;
        if ($attendanceRate >= 60) return 70;
        return 100;
    }

    /**
     * Calculate recent absence score.
     */
    protected function calculateRecentAbsenceScore(Collection $attendanceRecords): float
    {
        $recentRecords = $attendanceRecords->where('date', '>=', now()->subDays(14));
        
        if ($recentRecords->isEmpty()) {
            return 0;
        }

        $recentAbsences = $recentRecords->where('status', AttendanceStatus::ABSENT)->count();
        $totalRecentRecords = $recentRecords->count();

        // Score based on absence frequency in recent period
        $absenceRate = ($recentAbsences / $totalRecentRecords) * 100;
        
        if ($absenceRate >= 50) return 100;
        if ($absenceRate >= 30) return 80;
        if ($absenceRate >= 20) return 60;
        if ($absenceRate >= 10) return 40;
        return 0;
    }

    /**
     * Calculate consistency score.
     */
    protected function calculateConsistencyScore(Collection $attendanceRecords): float
    {
        if ($attendanceRecords->count() < 7) {
            return 100; // Not enough data, assume consistent
        }

        // Group by week and calculate weekly attendance rates
        $weeklyRates = $attendanceRecords->groupBy(function ($record) {
            $date = $record->date instanceof Carbon ? $record->date : Carbon::parse($record->date);
            return $date->format('Y-W');
        })->map(function ($weekRecords) {
            $total = $weekRecords->count();
            $present = $weekRecords->whereIn('status', [
                AttendanceStatus::PRESENT,
                AttendanceStatus::LATE,
                AttendanceStatus::EXCUSED
            ])->count();
            return $total > 0 ? ($present / $total) : 0;
        });

        if ($weeklyRates->count() < 2) {
            return 100;
        }

        // Calculate coefficient of variation
        $mean = $weeklyRates->avg();
        $variance = $weeklyRates->map(function ($rate) use ($mean) {
            return pow($rate - $mean, 2);
        })->avg();

        $standardDeviation = sqrt($variance);
        $coefficientOfVariation = $mean > 0 ? ($standardDeviation / $mean) : 0;

        // Convert to consistency score (lower variation = higher consistency)
        return max(0, 100 - ($coefficientOfVariation * 200));
    }

    /**
     * Calculate tardiness score.
     */
    protected function calculateTardinessScore(Collection $attendanceRecords): float
    {
        if ($attendanceRecords->isEmpty()) {
            return 0;
        }

        $lateCount = $attendanceRecords->where('status', AttendanceStatus::LATE)->count();
        $totalRecords = $attendanceRecords->count();

        $lateRate = ($lateCount / $totalRecords) * 100;

        if ($lateRate >= 40) return 100;
        if ($lateRate >= 25) return 80;
        if ($lateRate >= 15) return 60;
        if ($lateRate >= 10) return 40;
        return 0;
    }

    /**
     * Calculate trend score.
     */
    protected function calculateTrendScore(Collection $attendanceRecords): float
    {
        if ($attendanceRecords->count() < 14) {
            return 0; // Not enough data for trend analysis
        }

        $sortedRecords = $attendanceRecords->sortBy('date');
        $midpoint = $sortedRecords->count() / 2;

        $firstHalf = $sortedRecords->take($midpoint);
        $secondHalf = $sortedRecords->skip($midpoint);

        $firstHalfRate = $this->calculateAttendanceRate($firstHalf);
        $secondHalfRate = $this->calculateAttendanceRate($secondHalf);

        $decline = $firstHalfRate - $secondHalfRate;

        if ($decline >= 20) return 100;
        if ($decline >= 15) return 80;
        if ($decline >= 10) return 60;
        if ($decline >= 5) return 40;
        return 0;
    }

    /**
     * Determine risk level based on score.
     */
    protected function determineRiskLevel(float $riskScore): string
    {
        if ($riskScore >= 70) return 'high';
        if ($riskScore >= 40) return 'medium';
        return 'low';
    }

    /**
     * Calculate risk summary.
     */
    protected function calculateRiskSummary(Collection $assessments): array
    {
        $total = $assessments->count();

        if ($total === 0) {
            return [
                'total_students' => 0,
                'high_risk' => 0,
                'medium_risk' => 0,
                'low_risk' => 0,
                'average_risk_score' => 0,
                'average_attendance_rate' => 0,
            ];
        }

        $highRisk = $assessments->where('risk_level', 'high')->count();
        $mediumRisk = $assessments->where('risk_level', 'medium')->count();
        $lowRisk = $assessments->where('risk_level', 'low')->count();

        return [
            'total_students' => $total,
            'high_risk' => $highRisk,
            'medium_risk' => $mediumRisk,
            'low_risk' => $lowRisk,
            'high_risk_percentage' => round(($highRisk / $total) * 100, 1),
            'medium_risk_percentage' => round(($mediumRisk / $total) * 100, 1),
            'low_risk_percentage' => round(($lowRisk / $total) * 100, 1),
            'average_risk_score' => round($assessments->avg('risk_score'), 2),
            'average_attendance_rate' => round($assessments->avg('attendance_rate'), 2),
        ];
    }

    /**
     * Generate recommendations based on assessments.
     */
    protected function generateRecommendations(Collection $assessments): array
    {
        $recommendations = [];

        $highRiskCount = $assessments->where('risk_level', 'high')->count();
        $mediumRiskCount = $assessments->where('risk_level', 'medium')->count();

        if ($highRiskCount > 0) {
            $recommendations[] = [
                'priority' => 'high',
                'category' => 'immediate_intervention',
                'title' => 'Immediate Intervention Required',
                'description' => "There are {$highRiskCount} students at high risk of academic failure due to poor attendance.",
                'actions' => [
                    'Schedule parent conferences for high-risk students',
                    'Implement daily check-ins with counselors',
                    'Consider home visits for chronic absentees',
                    'Review and address potential barriers to attendance',
                ],
            ];
        }

        if ($mediumRiskCount > 0) {
            $recommendations[] = [
                'priority' => 'medium',
                'category' => 'preventive_measures',
                'title' => 'Preventive Intervention',
                'description' => "There are {$mediumRiskCount} students showing early warning signs.",
                'actions' => [
                    'Send attendance alerts to parents',
                    'Provide academic support and tutoring',
                    'Monitor attendance patterns closely',
                    'Implement attendance incentive programs',
                ],
            ];
        }

        // Common risk factors analysis
        $commonFactors = $this->analyzeCommonRiskFactors($assessments);
        if (!empty($commonFactors)) {
            $recommendations[] = [
                'priority' => 'medium',
                'category' => 'systemic_improvements',
                'title' => 'Address Common Issues',
                'description' => 'Multiple students share similar risk factors.',
                'actions' => $this->generateSystemicRecommendations($commonFactors),
            ];
        }

        return $recommendations;
    }

    /**
     * Generate individual recommendations.
     */
    protected function generateIndividualRecommendations(string $riskLevel, array $riskFactors): array
    {
        $recommendations = [];

        switch ($riskLevel) {
            case 'high':
                $recommendations[] = 'Schedule immediate parent conference';
                $recommendations[] = 'Assign dedicated counselor support';
                $recommendations[] = 'Develop individualized attendance plan';
                $recommendations[] = 'Consider home visit or family support services';
                break;

            case 'medium':
                $recommendations[] = 'Send attendance alert to parents';
                $recommendations[] = 'Monitor weekly attendance patterns';
                $recommendations[] = 'Provide academic support if needed';
                $recommendations[] = 'Implement attendance tracking system';
                break;

            case 'low':
                $recommendations[] = 'Continue regular monitoring';
                $recommendations[] = 'Recognize good attendance habits';
                $recommendations[] = 'Use as peer mentor for at-risk students';
                break;
        }

        // Add specific recommendations based on risk factors
        foreach ($riskFactors as $factor) {
            switch ($factor['factor']) {
                case 'frequent_tardiness':
                    $recommendations[] = 'Address transportation or morning routine issues';
                    break;
                case 'declining_trend':
                    $recommendations[] = 'Investigate recent changes in student circumstances';
                    break;
                case 'inconsistent_attendance':
                    $recommendations[] = 'Identify patterns and underlying causes';
                    break;
            }
        }

        return array_unique($recommendations);
    }

    /**
     * Analyze common risk factors.
     */
    protected function analyzeCommonRiskFactors(Collection $assessments): array
    {
        $factorCounts = [];

        foreach ($assessments as $assessment) {
            foreach ($assessment['risk_factors'] as $factor) {
                $factorType = $factor['factor'];
                if (!isset($factorCounts[$factorType])) {
                    $factorCounts[$factorType] = 0;
                }
                $factorCounts[$factorType]++;
            }
        }

        // Return factors affecting more than 20% of students
        $threshold = max(1, $assessments->count() * 0.2);
        return array_filter($factorCounts, function ($count) use ($threshold) {
            return $count >= $threshold;
        });
    }

    /**
     * Generate systemic recommendations.
     */
    protected function generateSystemicRecommendations(array $commonFactors): array
    {
        $recommendations = [];

        foreach ($commonFactors as $factor => $count) {
            switch ($factor) {
                case 'frequent_tardiness':
                    $recommendations[] = 'Review school start times and transportation schedules';
                    $recommendations[] = 'Implement tardiness reduction programs';
                    break;
                case 'low_attendance_rate':
                    $recommendations[] = 'Enhance engagement through improved curriculum';
                    $recommendations[] = 'Implement school-wide attendance incentives';
                    break;
                case 'inconsistent_attendance':
                    $recommendations[] = 'Improve communication with families';
                    $recommendations[] = 'Address systemic barriers to attendance';
                    break;
            }
        }

        return array_unique($recommendations);
    }
}
