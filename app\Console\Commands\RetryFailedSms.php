<?php

namespace App\Console\Commands;

use App\Models\SmsLog;
use App\Services\TextBeeSmsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RetryFailedSms extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sms:retry-failed 
                            {--teacher= : Retry failed messages for specific teacher ID}
                            {--hours=24 : Only retry messages failed within this many hours}
                            {--limit=100 : Maximum number of messages to retry}
                            {--max-retries=3 : Skip messages that have exceeded this retry count}
                            {--dry-run : Show what would be retried without actually sending}
                            {--force : Retry even messages that have exceeded max retries}';

    /**
     * The console command description.
     */
    protected $description = 'Retry failed SMS messages';

    protected TextBeeSmsService $smsService;

    public function __construct(TextBeeSmsService $smsService)
    {
        parent::__construct();
        $this->smsService = $smsService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $teacherId = $this->option('teacher');
        $hours = (int) $this->option('hours');
        $limit = (int) $this->option('limit');
        $maxRetries = (int) $this->option('max-retries');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Starting failed SMS retry process...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No messages will actually be sent');
        }

        // Build query for failed messages
        $query = SmsLog::retryable();

        // Filter by teacher if specified
        if ($teacherId) {
            $query->forTeacher((int) $teacherId);
        }

        // Filter by time window
        if ($hours > 0) {
            $query->where('failed_at', '>=', now()->subHours($hours));
        }

        // Filter by retry count unless forced
        if (!$force) {
            $query->where('retry_count', '<', $maxRetries);
        }

        $failedMessages = $query->limit($limit)
            ->orderBy('failed_at', 'desc')
            ->get();

        if ($failedMessages->isEmpty()) {
            $this->info('No failed messages found to retry.');
            return self::SUCCESS;
        }

        $this->info("Found {$failedMessages->count()} failed messages to retry");

        if ($dryRun) {
            $this->displayRetryPreview($failedMessages);
            return self::SUCCESS;
        }

        // Confirm before proceeding
        if (!$this->confirm('Do you want to proceed with retrying these messages?')) {
            $this->info('Retry cancelled.');
            return self::SUCCESS;
        }

        $processed = 0;
        $successful = 0;
        $failed = 0;

        $progressBar = $this->output->createProgressBar($failedMessages->count());
        $progressBar->start();

        foreach ($failedMessages as $smsLog) {
            try {
                $this->line("\nRetrying message ID: {$smsLog->id}");
                $this->line("To: {$smsLog->recipient_phone}");
                $this->line("Original failure: {$smsLog->failure_reason}");
                $this->line("Retry count: {$smsLog->retry_count}");

                // Increment retry count
                $smsLog->incrementRetry();

                // Attempt to resend
                $result = $this->retrySmsMessage($smsLog);

                if ($result['success']) {
                    $smsLog->markAsSent($result['message_id'], $result['provider_response'] ?? []);
                    $successful++;
                    $this->line("✓ Retry successful");
                } else {
                    $smsLog->markAsFailed($result['error'], $result['provider_response'] ?? []);
                    $failed++;
                    $this->line("✗ Retry failed: {$result['error']}");
                }

                $processed++;
                $progressBar->advance();

                // Small delay to respect rate limits
                sleep(1);

            } catch (\Exception $e) {
                $this->error("Exception retrying message {$smsLog->id}: {$e->getMessage()}");
                
                $smsLog->markAsFailed('Retry exception: ' . $e->getMessage());
                $failed++;
                $processed++;
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display summary
        $this->info('Failed SMS Retry Complete');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Processed', $processed],
                ['Successful Retries', $successful],
                ['Failed Retries', $failed],
                ['Success Rate', $processed > 0 ? round(($successful / $processed) * 100, 2) . '%' : '0%'],
            ]
        );

        // Log summary
        Log::info('Failed SMS retry completed', [
            'processed' => $processed,
            'successful' => $successful,
            'failed' => $failed,
            'teacher_id' => $teacherId,
            'hours_window' => $hours,
        ]);

        return self::SUCCESS;
    }

    /**
     * Display preview of messages to be retried.
     */
    protected function displayRetryPreview($failedMessages): void
    {
        $this->info('Messages that would be retried:');
        
        $tableData = [];
        foreach ($failedMessages as $smsLog) {
            $tableData[] = [
                $smsLog->id,
                $smsLog->recipient_phone,
                $smsLog->message_type->value,
                $smsLog->retry_count,
                $smsLog->failed_at?->format('Y-m-d H:i:s'),
                substr($smsLog->failure_reason ?? '', 0, 30) . '...',
            ];
        }

        $this->table(
            ['ID', 'Phone', 'Type', 'Retries', 'Failed At', 'Reason'],
            $tableData
        );
    }

    /**
     * Retry sending an SMS message.
     */
    protected function retrySmsMessage(SmsLog $smsLog): array
    {
        try {
            // Use the SMS service to resend the message
            $result = $this->smsService->sendSMS(
                phone: $smsLog->recipient_phone,
                message: $smsLog->message_content,
                type: $smsLog->message_type,
                teacherId: $smsLog->teacher_id,
                recipientName: $smsLog->recipient_name,
                recipientType: $smsLog->recipient_type,
                batchId: $smsLog->batch_id,
                templateUsed: $smsLog->template_used,
                metadata: $smsLog->metadata ?? []
            );

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
