<?php

namespace Tests\Feature;

use App\Enums\SmsStatus;
use App\Enums\SmsType;
use App\Models\SmsLog;
use App\Models\SmsTemplate;
use App\Models\Teacher;
use App\Services\TextBeeSmsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class SmsControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Teacher $teacher;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->teacher = Teacher::factory()->create();
        
        // Mock SMS service configuration
        config(['services.sms.textbee.api_key' => 'test-key']);
    }

    /** @test */
    public function authenticated_user_can_send_sms()
    {
        Sanctum::actingAs($this->teacher);

        // Mock successful SMS sending
        Http::fake([
            '*' => Http::response([
                'success' => true,
                'message_id' => 'msg_test_123',
                'cost' => 1.00,
            ], 200)
        ]);

        $response = $this->postJson('/api/sms/send', [
            'phone' => '+639123456789',
            'message' => 'Test SMS message',
            'type' => 'manual',
            'recipient_name' => 'Test Parent',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'SMS sent successfully',
                ]);

        $this->assertDatabaseHas('sms_logs', [
            'teacher_id' => $this->teacher->id,
            'recipient_phone' => '+639123456789',
            'message_type' => SmsType::MANUAL->value,
        ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_send_sms()
    {
        $response = $this->postJson('/api/sms/send', [
            'phone' => '+639123456789',
            'message' => 'Test SMS message',
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_validates_phone_number_format()
    {
        Sanctum::actingAs($this->teacher);

        $response = $this->postJson('/api/sms/send', [
            'phone' => 'invalid-phone',
            'message' => 'Test SMS message',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone']);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        Sanctum::actingAs($this->teacher);

        $response = $this->postJson('/api/sms/send', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone', 'message']);
    }

    /** @test */
    public function authenticated_user_can_send_bulk_sms()
    {
        Sanctum::actingAs($this->teacher);

        // Mock successful bulk SMS
        Http::fake([
            '*' => Http::response([
                'success' => true,
                'message_id' => 'bulk_123',
                'cost' => 2.00,
            ], 200)
        ]);

        $recipients = [
            ['phone' => '+639123456789', 'name' => 'Parent 1'],
            ['phone' => '+639987654321', 'name' => 'Parent 2'],
        ];

        $response = $this->postJson('/api/sms/send-bulk', [
            'recipients' => $recipients,
            'message' => 'Bulk test message',
            'type' => 'bulk',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Bulk SMS processed successfully',
                ]);
    }

    /** @test */
    public function it_can_get_sms_logs()
    {
        Sanctum::actingAs($this->teacher);

        // Create test SMS logs
        SmsLog::factory()->count(3)->create([
            'teacher_id' => $this->teacher->id,
        ]);

        $response = $this->getJson('/api/sms/logs');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'SMS logs retrieved successfully',
                ])
                ->assertJsonStructure([
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'teacher_id',
                                'recipient_phone',
                                'message_content',
                                'status',
                                'created_at',
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function it_can_filter_sms_logs_by_status()
    {
        Sanctum::actingAs($this->teacher);

        // Create SMS logs with different statuses
        SmsLog::factory()->create([
            'teacher_id' => $this->teacher->id,
            'status' => SmsStatus::SENT,
        ]);
        
        SmsLog::factory()->create([
            'teacher_id' => $this->teacher->id,
            'status' => SmsStatus::FAILED,
        ]);

        $response = $this->getJson('/api/sms/logs?status=sent');

        $response->assertStatus(200);
        
        $logs = $response->json('data.data');
        $this->assertCount(1, $logs);
        $this->assertEquals('sent', $logs[0]['status']);
    }

    /** @test */
    public function it_can_get_delivery_status()
    {
        Sanctum::actingAs($this->teacher);

        // Mock delivery status response
        Http::fake([
            '*' => Http::response([
                'success' => true,
                'message_id' => 'msg_123',
                'status' => 'delivered',
            ], 200)
        ]);

        $response = $this->getJson('/api/sms/status/msg_123');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Status retrieved successfully',
                ]);
    }

    /** @test */
    public function it_can_get_sms_templates()
    {
        Sanctum::actingAs($this->teacher);

        // Create test templates
        SmsTemplate::factory()->count(3)->create([
            'is_active' => true,
        ]);

        $response = $this->getJson('/api/sms/templates');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Templates retrieved successfully',
                ])
                ->assertJsonCount(3, 'data');
    }

    /** @test */
    public function it_can_preview_template()
    {
        Sanctum::actingAs($this->teacher);

        $response = $this->getJson('/api/sms/templates/attendance_absent/preview');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'template_name',
                        'sample_output',
                        'character_count',
                        'estimated_parts',
                        'estimated_cost',
                    ]
                ]);
    }

    /** @test */
    public function it_can_validate_phone_numbers()
    {
        Sanctum::actingAs($this->teacher);

        $response = $this->postJson('/api/sms/validate-phone', [
            'phone' => '+639123456789',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Phone validation completed',
                    'data' => [
                        'phone' => '+639123456789',
                        'is_valid' => true,
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_cost_summary()
    {
        Sanctum::actingAs($this->teacher);

        $response = $this->getJson('/api/sms/cost-summary');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Cost summary retrieved successfully',
                ]);
    }

    /** @test */
    public function it_can_retry_failed_sms()
    {
        Sanctum::actingAs($this->teacher);

        // Mock retry response
        Http::fake([
            '*' => Http::response([
                'success' => true,
                'retried_count' => 2,
                'successful_count' => 1,
            ], 200)
        ]);

        $response = $this->postJson('/api/sms/retry-failed', [
            'teacher_id' => $this->teacher->id,
            'max_retries' => 3,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);
    }

    /** @test */
    public function it_can_generate_reports()
    {
        Sanctum::actingAs($this->teacher);

        $response = $this->getJson('/api/sms/report?teacher_id=' . $this->teacher->id);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);
    }
}
