<?php

namespace App\Http\Requests;

use App\Enums\SmsType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendSmsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'phone' => [
                'required',
                'string',
                'regex:/^(\+63|63|0)?9\d{9}$/',
            ],
            'message' => [
                'required',
                'string',
                'max:1600',
            ],
            'type' => [
                'nullable',
                'string',
                Rule::in(array_column(SmsType::cases(), 'value')),
            ],
            'recipient_name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'recipient_type' => [
                'nullable',
                'string',
                'in:parent,student,teacher,admin',
            ],
            'template_name' => [
                'nullable',
                'string',
                'exists:sms_templates,name',
            ],
            'metadata' => [
                'nullable',
                'array',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please provide a valid Philippine mobile number (e.g., +639123456789, 09123456789).',
            'message.required' => 'Message content is required.',
            'message.max' => 'Message cannot exceed 1600 characters.',
            'type.in' => 'Invalid SMS type provided.',
            'recipient_type.in' => 'Recipient type must be one of: parent, student, teacher, admin.',
            'template_name.exists' => 'The specified template does not exist.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and format phone number
        if ($this->has('phone')) {
            $phone = preg_replace('/[^0-9+]/', '', $this->phone);
            
            // Convert to international format
            if (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
                $phone = '+63' . substr($phone, 1);
            } elseif (strlen($phone) === 10 && substr($phone, 0, 1) === '9') {
                $phone = '+63' . $phone;
            } elseif (strlen($phone) === 12 && substr($phone, 0, 2) === '63') {
                $phone = '+' . $phone;
            }
            
            $this->merge(['phone' => $phone]);
        }

        // Set default type if not provided
        if (!$this->has('type')) {
            $this->merge(['type' => 'manual']);
        }
    }
}
