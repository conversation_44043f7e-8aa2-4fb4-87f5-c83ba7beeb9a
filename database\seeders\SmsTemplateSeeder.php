<?php

namespace Database\Seeders;

use App\Models\SmsTemplate;
use Illuminate\Database\Seeder;

class SmsTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            // Attendance Templates
            [
                'name' => 'attendance_present',
                'display_name' => 'Student Present Alert',
                'description' => 'Notification when student is marked present',
                'content' => 'Good day {parent_name}! Your child {student_name} has arrived at school and is present for {subject} class on {date} at {time}. - {school_name}',
                'variables' => ['parent_name', 'student_name', 'subject', 'date', 'time', 'school_name'],
                'category' => 'attendance',
                'type' => 'notification',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'attendance_absent',
                'display_name' => 'Student Absent Alert',
                'description' => 'Notification when student is marked absent',
                'content' => 'Dear {parent_name}, your child {student_name} was marked ABSENT in {subject} class on {date}. Please contact the school if this is incorrect. - {school_name}',
                'variables' => ['parent_name', 'student_name', 'subject', 'date', 'school_name'],
                'category' => 'attendance',
                'type' => 'alert',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'attendance_late',
                'display_name' => 'Student Late Alert',
                'description' => 'Notification when student is marked late',
                'content' => 'Dear {parent_name}, your child {student_name} arrived LATE for {subject} class on {date} at {time}. Please ensure punctuality. - {school_name}',
                'variables' => ['parent_name', 'student_name', 'subject', 'date', 'time', 'school_name'],
                'category' => 'attendance',
                'type' => 'alert',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'attendance_excused',
                'display_name' => 'Student Excused Alert',
                'description' => 'Notification when student absence is excused',
                'content' => 'Dear {parent_name}, your child {student_name} has an EXCUSED absence for {subject} class on {date}. Thank you for informing us. - {school_name}',
                'variables' => ['parent_name', 'student_name', 'subject', 'date', 'school_name'],
                'category' => 'attendance',
                'type' => 'notification',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],

            // General Alert Templates
            [
                'name' => 'emergency_alert',
                'display_name' => 'Emergency Alert',
                'description' => 'Emergency notification template',
                'content' => 'EMERGENCY ALERT: {message} Please contact the school immediately at {contact_number}. - {school_name}',
                'variables' => ['message', 'contact_number', 'school_name'],
                'category' => 'alert',
                'type' => 'alert',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'class_cancellation',
                'display_name' => 'Class Cancellation',
                'description' => 'Notification for cancelled classes',
                'content' => 'Dear Parents, {subject} class on {date} at {time} has been CANCELLED due to {reason}. We will notify you of the makeup schedule. - {school_name}',
                'variables' => ['subject', 'date', 'time', 'reason', 'school_name'],
                'category' => 'announcement',
                'type' => 'notification',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'early_dismissal',
                'display_name' => 'Early Dismissal',
                'description' => 'Notification for early dismissal',
                'content' => 'Dear {parent_name}, your child {student_name} will be dismissed early today at {time} due to {reason}. Please arrange pickup accordingly. - {school_name}',
                'variables' => ['parent_name', 'student_name', 'time', 'reason', 'school_name'],
                'category' => 'alert',
                'type' => 'notification',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],

            // Reminder Templates
            [
                'name' => 'exam_reminder',
                'display_name' => 'Exam Reminder',
                'description' => 'Reminder for upcoming exams',
                'content' => 'Reminder: {student_name} has a {subject} exam on {date} at {time}. Please ensure your child is prepared and arrives on time. - {school_name}',
                'variables' => ['student_name', 'subject', 'date', 'time', 'school_name'],
                'category' => 'reminder',
                'type' => 'reminder',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'assignment_reminder',
                'display_name' => 'Assignment Reminder',
                'description' => 'Reminder for assignment due dates',
                'content' => 'Reminder: {student_name} has a {subject} assignment due on {date}. Please ensure it is completed and submitted on time. - {school_name}',
                'variables' => ['student_name', 'subject', 'date', 'school_name'],
                'category' => 'reminder',
                'type' => 'reminder',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'parent_meeting',
                'display_name' => 'Parent Meeting Reminder',
                'description' => 'Reminder for parent-teacher meetings',
                'content' => 'Dear {parent_name}, you have a scheduled meeting with {teacher_name} on {date} at {time} to discuss {student_name}\'s progress. - {school_name}',
                'variables' => ['parent_name', 'teacher_name', 'date', 'time', 'student_name', 'school_name'],
                'category' => 'reminder',
                'type' => 'reminder',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],

            // General Announcement Templates
            [
                'name' => 'school_event',
                'display_name' => 'School Event Announcement',
                'description' => 'General school event announcements',
                'content' => 'Dear Parents, we are pleased to announce {event_name} on {date} at {time}. {additional_info} - {school_name}',
                'variables' => ['event_name', 'date', 'time', 'additional_info', 'school_name'],
                'category' => 'announcement',
                'type' => 'announcement',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
            [
                'name' => 'weather_advisory',
                'display_name' => 'Weather Advisory',
                'description' => 'Weather-related school announcements',
                'content' => 'Weather Advisory: Due to {weather_condition}, classes are {status} on {date}. Please stay safe and monitor updates. - {school_name}',
                'variables' => ['weather_condition', 'status', 'date', 'school_name'],
                'category' => 'announcement',
                'type' => 'alert',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],

            // Custom/Manual Templates
            [
                'name' => 'custom_message',
                'display_name' => 'Custom Message',
                'description' => 'Template for custom messages',
                'content' => 'Dear {recipient_name}, {message} - {sender_name}',
                'variables' => ['recipient_name', 'message', 'sender_name'],
                'category' => 'general',
                'type' => 'notification',
                'is_system' => true,
                'is_active' => true,
                'character_limit' => 160,
            ],
        ];

        foreach ($templates as $template) {
            SmsTemplate::updateOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }
}
