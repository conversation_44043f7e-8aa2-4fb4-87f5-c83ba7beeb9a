@extends('layouts.app')

@section('title', 'Class Attendance')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Class Attendance</h1>
                <p class="text-gray-600 mt-1">
                    {{ $attendanceData['grade_level'] ? 'Grade ' . $attendanceData['grade_level'] : '' }}
                    {{ $attendanceData['section'] ? ' - Section ' . $attendanceData['section'] : '' }}
                </p>
            </div>
            <div class="flex space-x-3">
                <button type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-qrcode mr-2"></i>
                    Scan QR
                </button>
                <button type="button" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Mark Attendance
                </button>
            </div>
        </div>
    </div>

    <!-- Attendance Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Students</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $attendanceData['statistics']['total_students'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Present</p>
                    <p class="text-2xl font-bold text-green-600">{{ $attendanceData['statistics']['present_count'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <i class="fas fa-times text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Absent</p>
                    <p class="text-2xl font-bold text-red-600">{{ $attendanceData['statistics']['absent_count'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Late</p>
                    <p class="text-2xl font-bold text-yellow-600">{{ $attendanceData['statistics']['late_count'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Rate -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Attendance Rate</h3>
                <p class="text-sm text-gray-600">{{ $attendanceData['date'] }}</p>
            </div>
            <div class="text-right">
                <div class="text-3xl font-bold {{ $attendanceData['statistics']['attendance_rate'] >= 90 ? 'text-green-600' : ($attendanceData['statistics']['attendance_rate'] >= 75 ? 'text-yellow-600' : 'text-red-600') }}">
                    {{ $attendanceData['statistics']['attendance_rate'] }}%
                </div>
                <div class="w-32 bg-gray-200 rounded-full h-2 mt-2">
                    <div class="bg-{{ $attendanceData['statistics']['attendance_rate'] >= 90 ? 'green' : ($attendanceData['statistics']['attendance_rate'] >= 75 ? 'yellow' : 'red') }}-600 h-2 rounded-full" 
                         style="width: {{ $attendanceData['statistics']['attendance_rate'] }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Students List -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Students</h3>
                <div class="flex space-x-2">
                    <button type="button" class="text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md transition-colors">
                        <i class="fas fa-filter mr-1"></i>
                        Filter
                    </button>
                    <button type="button" class="text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md transition-colors">
                        <i class="fas fa-download mr-1"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Student
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Student ID
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Time In
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Time Out
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($attendanceData['students'] as $student)
                        @php
                            $attendance = $student->attendance->first();
                            $status = $attendance?->status ?? null;
                        @endphp
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($student->photo_path)
                                            <img class="h-10 w-10 rounded-full object-cover" src="{{ Storage::url($student->photo_path) }}" alt="{{ $student->full_name }}">
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-600"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $student->full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $student->grade_level }} - {{ $student->section }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $student->student_id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($status)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {{ $status->value === 'present' ? 'bg-green-100 text-green-800' : '' }}
                                        {{ $status->value === 'absent' ? 'bg-red-100 text-red-800' : '' }}
                                        {{ $status->value === 'late' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                        {{ $status->value === 'excused' ? 'bg-blue-100 text-blue-800' : '' }}">
                                        {{ $status->label() }}
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Not Marked
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $attendance?->formatted_time_in ?? '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $attendance?->formatted_time_out ?? '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    @if($attendance)
                                        <button type="button" class="text-blue-600 hover:text-blue-900 transition-colors" 
                                                onclick="editAttendance({{ $attendance->id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    @else
                                        <button type="button" class="text-green-600 hover:text-green-900 transition-colors"
                                                onclick="markAttendance({{ $student->id }})">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    @endif
                                    <button type="button" class="text-gray-600 hover:text-gray-900 transition-colors"
                                            onclick="viewHistory({{ $student->id }})">
                                        <i class="fas fa-history"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                No students found for this class.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Last Updated -->
    <div class="mt-4 text-center text-sm text-gray-500">
        Last updated: {{ $attendanceData['last_updated'] }}
    </div>
</div>

<script>
function editAttendance(attendanceId) {
    // Implementation for editing attendance
    console.log('Edit attendance:', attendanceId);
}

function markAttendance(studentId) {
    // Implementation for marking attendance
    console.log('Mark attendance for student:', studentId);
}

function viewHistory(studentId) {
    // Implementation for viewing student history
    console.log('View history for student:', studentId);
}
</script>
@endsection
