<?php

namespace App\Enums;

enum QueueStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case PROCESSED = 'processed';
    case FAILED = 'failed';
    case CANCELLED = 'cancelled';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pending',
            self::PROCESSING => 'Processing',
            self::PROCESSED => 'Processed',
            self::FAILED => 'Failed',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get status color for UI.
     */
    public function color(): string
    {
        return match($this) {
            self::PENDING => 'yellow',
            self::PROCESSING => 'blue',
            self::PROCESSED => 'green',
            self::FAILED => 'red',
            self::CANCELLED => 'gray',
        };
    }

    /**
     * Check if status is final.
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::PROCESSED, self::FAILED, self::CANCELLED]);
    }

    /**
     * Check if status can be retried.
     */
    public function canRetry(): bool
    {
        return $this === self::FAILED;
    }

    /**
     * Check if status can be cancelled.
     */
    public function canCancel(): bool
    {
        return in_array($this, [self::PENDING, self::PROCESSING]);
    }
}
