<?php

namespace App\Enums;

enum SmsProvider: string
{
    case TEXTBEE = 'textbee';
    case SEMAPHORE = 'semaphore';
    case TWILIO = 'twilio';
    case ITEXMO = 'itexmo';
    case LOG = 'log';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match($this) {
            self::TEXTBEE => 'TextBee',
            self::SEMAPHORE => 'Semaphore',
            self::TWILIO => 'Twilio',
            self::ITEXMO => 'iTexMo',
            self::LOG => 'Log Only',
        };
    }

    /**
     * Get provider website URL.
     */
    public function website(): string
    {
        return match($this) {
            self::TEXTBEE => 'https://textbee.ph',
            self::SEMAPHORE => 'https://semaphore.co',
            self::TWILIO => 'https://twilio.com',
            self::ITEXMO => 'https://itexmo.com',
            self::LOG => '',
        };
    }

    /**
     * Get provider API endpoint.
     */
    public function apiEndpoint(): string
    {
        return match($this) {
            self::TEXTBEE => 'https://api.textbee.ph/api/v1/sms/send',
            self::SEMAPHORE => 'https://api.semaphore.co/api/v4/messages',
            self::TWILIO => 'https://api.twilio.com/2010-04-01/Accounts/{account_sid}/Messages.json',
            self::ITEXMO => 'https://www.itexmo.com/php_api/api.php',
            self::LOG => '',
        };
    }

    /**
     * Get estimated cost per SMS in PHP.
     */
    public function estimatedCost(): float
    {
        return match($this) {
            self::TEXTBEE => 0.50,
            self::SEMAPHORE => 2.50,
            self::TWILIO => 3.00,
            self::ITEXMO => 1.00,
            self::LOG => 0.00,
        };
    }

    /**
     * Check if provider supports delivery status.
     */
    public function supportsDeliveryStatus(): bool
    {
        return match($this) {
            self::TEXTBEE => true,
            self::SEMAPHORE => true,
            self::TWILIO => true,
            self::ITEXMO => false,
            self::LOG => false,
        };
    }

    /**
     * Check if provider supports bulk sending.
     */
    public function supportsBulkSending(): bool
    {
        return match($this) {
            self::TEXTBEE => true,
            self::SEMAPHORE => true,
            self::TWILIO => false,
            self::ITEXMO => false,
            self::LOG => true,
        };
    }

    /**
     * Get rate limit per minute.
     */
    public function rateLimit(): int
    {
        return match($this) {
            self::TEXTBEE => 60,
            self::SEMAPHORE => 100,
            self::TWILIO => 200,
            self::ITEXMO => 30,
            self::LOG => 1000,
        };
    }
}
