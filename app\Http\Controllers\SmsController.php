<?php

namespace App\Http\Controllers;

use App\Enums\SmsType;
use App\Helpers\SmsFormatter;
use App\Http\Requests\SendBulkSmsRequest;
use App\Http\Requests\SendSmsRequest;
use App\Models\SmsLog;
use App\Models\SmsQueue;
use App\Models\SmsTemplate;
use App\Models\SmsUsageTracking;
use App\Services\SmsCostTracker;
use App\Services\TextBeeSmsService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SmsController extends Controller
{
    protected TextBeeSmsService $smsService;
    protected SmsCostTracker $costTracker;

    public function __construct(TextBeeSmsService $smsService, SmsCostTracker $costTracker)
    {
        $this->smsService = $smsService;
        $this->costTracker = $costTracker;
        $this->middleware('auth');
    }

    /**
     * Send a single SMS message.
     */
    public function sendSms(SendSmsRequest $request): JsonResponse
    {
        try {
            $result = $this->smsService->sendSMS(
                phone: $request->phone,
                message: $request->message,
                type: SmsType::from($request->type ?? 'manual'),
                teacherId: Auth::id(),
                recipientName: $request->recipient_name,
                recipientType: $request->recipient_type ?? 'parent',
                templateUsed: $request->template_name,
                metadata: $request->metadata ?? []
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'SMS sent successfully' : 'Failed to send SMS',
                'data' => $result,
            ], $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending SMS',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send bulk SMS messages.
     */
    public function sendBulkSms(SendBulkSmsRequest $request): JsonResponse
    {
        try {
            $result = $this->smsService->sendBulkSMS(
                recipients: $request->recipients,
                message: $request->message,
                teacherId: Auth::id(),
                templateName: $request->template_name,
                options: [
                    'queue' => $request->queue ?? true,
                    'scheduled_at' => $request->scheduled_at,
                    'priority' => $request->priority ?? 5,
                ]
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Bulk SMS processed successfully' : 'Failed to process bulk SMS',
                'data' => $result,
            ], $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending bulk SMS',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery status of a message.
     */
    public function getDeliveryStatus(string $messageId): JsonResponse
    {
        try {
            $result = $this->smsService->getDeliveryStatus($messageId);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Status retrieved successfully' : 'Failed to get status',
                'data' => $result,
            ], $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting delivery status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get SMS logs with filtering.
     */
    public function getLogs(Request $request): JsonResponse
    {
        try {
            $query = SmsLog::with(['teacher'])
                ->when($request->teacher_id, fn($q) => $q->forTeacher($request->teacher_id))
                ->when($request->status, fn($q) => $q->withStatus($request->status))
                ->when($request->type, fn($q) => $q->ofType($request->type))
                ->when($request->start_date, fn($q) => $q->where('created_at', '>=', $request->start_date))
                ->when($request->end_date, fn($q) => $q->where('created_at', '<=', $request->end_date))
                ->orderBy('created_at', 'desc');

            $logs = $query->paginate($request->per_page ?? 15);

            return response()->json([
                'success' => true,
                'message' => 'SMS logs retrieved successfully',
                'data' => $logs,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving SMS logs',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get SMS queue status.
     */
    public function getQueueStatus(Request $request): JsonResponse
    {
        try {
            $query = SmsQueue::with(['teacher'])
                ->when($request->teacher_id, fn($q) => $q->where('teacher_id', $request->teacher_id))
                ->when($request->status, fn($q) => $q->where('status', $request->status))
                ->when($request->batch_id, fn($q) => $q->where('batch_id', $request->batch_id))
                ->orderBy('created_at', 'desc');

            $queue = $query->paginate($request->per_page ?? 15);

            // Add summary statistics
            $summary = [
                'total_pending' => SmsQueue::where('status', 'pending')->count(),
                'total_processing' => SmsQueue::where('status', 'processing')->count(),
                'total_processed' => SmsQueue::where('status', 'processed')->count(),
                'total_failed' => SmsQueue::where('status', 'failed')->count(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Queue status retrieved successfully',
                'data' => $queue,
                'summary' => $summary,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving queue status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Retry failed SMS messages.
     */
    public function retryFailed(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'teacher_id' => 'nullable|integer|exists:teachers,id',
                'max_retries' => 'nullable|integer|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $result = $this->smsService->retryFailedSMS(
                teacherId: $request->teacher_id,
                maxRetries: $request->max_retries
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Failed SMS retry completed' : 'Failed to retry SMS messages',
                'data' => $result,
            ], $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrying failed SMS',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate SMS usage report.
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'teacher_id' => 'nullable|integer|exists:teachers,id',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'period' => 'nullable|in:daily,weekly,monthly',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $result = $this->smsService->generateSMSReport(
                teacherId: $request->teacher_id,
                startDate: $request->start_date,
                endDate: $request->end_date,
                period: $request->period ?? 'daily'
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Report generated successfully' : 'Failed to generate report',
                'data' => $result,
            ], $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get cost summary for teacher.
     */
    public function getCostSummary(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->teacher_id ?? Auth::id();
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

            $summary = $this->costTracker->getTeacherCostSummary($teacherId, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'message' => 'Cost summary retrieved successfully',
                'data' => $summary,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving cost summary',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get SMS templates.
     */
    public function getTemplates(Request $request): JsonResponse
    {
        try {
            $query = SmsTemplate::where('is_active', true)
                ->when($request->category, fn($q) => $q->where('category', $request->category))
                ->when($request->type, fn($q) => $q->where('type', $request->type))
                ->orderBy('display_name');

            $templates = $query->get();

            return response()->json([
                'success' => true,
                'message' => 'Templates retrieved successfully',
                'data' => $templates,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving templates',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Preview template with sample data.
     */
    public function previewTemplate(string $templateName): JsonResponse
    {
        try {
            $sampleOutput = SmsFormatter::getSampleOutput($templateName);

            if (!$sampleOutput) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Template preview generated successfully',
                'data' => [
                    'template_name' => $templateName,
                    'sample_output' => $sampleOutput,
                    'character_count' => strlen($sampleOutput),
                    'estimated_parts' => SmsFormatter::calculateParts($sampleOutput),
                    'estimated_cost' => SmsFormatter::estimateCost($sampleOutput),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating template preview',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate phone number.
     */
    public function validatePhone(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $isValid = $this->smsService->validatePhone($request->phone);

            return response()->json([
                'success' => true,
                'message' => 'Phone validation completed',
                'data' => [
                    'phone' => $request->phone,
                    'is_valid' => $isValid,
                    'formatted' => $isValid ? $request->phone : null,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error validating phone number',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
