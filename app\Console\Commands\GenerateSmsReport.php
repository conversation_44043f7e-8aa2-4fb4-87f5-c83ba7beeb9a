<?php

namespace App\Console\Commands;

use App\Services\TextBeeSmsService;
use App\Services\SmsCostTracker;
use App\Models\SmsUsageTracking;
use App\Models\Teacher;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class GenerateSmsReport extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sms:generate-report 
                            {--teacher= : Generate report for specific teacher ID}
                            {--start-date= : Start date for report (YYYY-MM-DD)}
                            {--end-date= : End date for report (YYYY-MM-DD)}
                            {--period=daily : Report period (daily, weekly, monthly)}
                            {--format=table : Output format (table, json, csv, file)}
                            {--output= : Output file path (for file format)}
                            {--email= : Email address to send report to}';

    /**
     * The console command description.
     */
    protected $description = 'Generate SMS usage and cost reports';

    protected TextBeeSmsService $smsService;
    protected SmsCostTracker $costTracker;

    public function __construct(TextBeeSmsService $smsService, SmsCostTracker $costTracker)
    {
        parent::__construct();
        $this->smsService = $smsService;
        $this->costTracker = $costTracker;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $teacherId = $this->option('teacher');
        $startDate = $this->option('start-date');
        $endDate = $this->option('end-date');
        $period = $this->option('period');
        $format = $this->option('format');
        $outputFile = $this->option('output');
        $email = $this->option('email');

        // Parse dates
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        $this->info("Generating SMS report from {$startDate->toDateString()} to {$endDate->toDateString()}");

        // Generate report data
        if ($teacherId) {
            $reportData = $this->generateTeacherReport((int) $teacherId, $startDate, $endDate, $period);
        } else {
            $reportData = $this->generateSchoolReport($startDate, $endDate, $period);
        }

        // Output report based on format
        switch ($format) {
            case 'json':
                $this->outputJson($reportData);
                break;
            case 'csv':
                $this->outputCsv($reportData, $outputFile);
                break;
            case 'file':
                $this->outputFile($reportData, $outputFile);
                break;
            default:
                $this->outputTable($reportData);
                break;
        }

        // Send email if requested
        if ($email) {
            $this->sendReportEmail($reportData, $email);
        }

        return self::SUCCESS;
    }

    /**
     * Generate teacher-specific report.
     */
    protected function generateTeacherReport(int $teacherId, Carbon $startDate, Carbon $endDate, string $period): array
    {
        $teacher = Teacher::find($teacherId);
        if (!$teacher) {
            $this->error("Teacher with ID {$teacherId} not found.");
            return [];
        }

        $costSummary = $this->costTracker->getTeacherCostSummary($teacherId, $startDate, $endDate);
        $projections = $this->costTracker->getCostProjections($teacherId, 30);

        return [
            'type' => 'teacher',
            'teacher' => [
                'id' => $teacher->id,
                'name' => $teacher->full_name,
                'email' => $teacher->email,
            ],
            'period' => $costSummary['period'],
            'summary' => $costSummary['summary'],
            'message_types' => $costSummary['message_types'],
            'daily_breakdown' => $costSummary['daily_breakdown'],
            'projections' => $projections,
        ];
    }

    /**
     * Generate school-wide report.
     */
    protected function generateSchoolReport(Carbon $startDate, Carbon $endDate, string $period): array
    {
        $schoolSummary = $this->costTracker->getSchoolCostSummary($startDate, $endDate);

        return [
            'type' => 'school',
            'period' => $schoolSummary['period'],
            'summary' => $schoolSummary['summary'],
            'top_teachers' => $schoolSummary['top_teachers'],
            'provider_stats' => $schoolSummary['provider_stats'],
            'monthly_trend' => $schoolSummary['monthly_trend'],
        ];
    }

    /**
     * Output report as table.
     */
    protected function outputTable(array $reportData): void
    {
        if (empty($reportData)) {
            return;
        }

        $this->info('SMS Usage Report');
        $this->line('==================');

        // Summary section
        if (isset($reportData['summary'])) {
            $this->info('Summary:');
            $summaryTable = [];
            foreach ($reportData['summary'] as $key => $value) {
                $summaryTable[] = [ucwords(str_replace('_', ' ', $key)), $this->formatValue($key, $value)];
            }
            $this->table(['Metric', 'Value'], $summaryTable);
            $this->newLine();
        }

        // Teacher-specific sections
        if ($reportData['type'] === 'teacher') {
            // Message types
            if (isset($reportData['message_types'])) {
                $this->info('Message Types:');
                $typeTable = [];
                foreach ($reportData['message_types'] as $type => $count) {
                    $typeTable[] = [ucwords($type), $count];
                }
                $this->table(['Type', 'Count'], $typeTable);
                $this->newLine();
            }

            // Projections
            if (isset($reportData['projections'])) {
                $this->info('30-Day Projections:');
                $projTable = [
                    ['Projected Cost', '₱' . number_format($reportData['projections']['projected_cost'], 2)],
                    ['Projected Messages', number_format($reportData['projections']['projected_messages'])],
                    ['Confidence', ucwords($reportData['projections']['confidence'])],
                ];
                $this->table(['Metric', 'Value'], $projTable);
            }
        }

        // School-wide sections
        if ($reportData['type'] === 'school') {
            // Top teachers
            if (isset($reportData['top_teachers']) && !empty($reportData['top_teachers'])) {
                $this->info('Top Teachers by Cost:');
                $teacherTable = [];
                foreach (array_slice($reportData['top_teachers'], 0, 10) as $teacher) {
                    $teacherTable[] = [
                        $teacher['teacher']['full_name'] ?? 'Unknown',
                        number_format($teacher['total_messages']),
                        '₱' . number_format($teacher['total_cost'], 2),
                        number_format($teacher['success_rate'], 1) . '%',
                    ];
                }
                $this->table(['Teacher', 'Messages', 'Cost', 'Success Rate'], $teacherTable);
                $this->newLine();
            }

            // Provider stats
            if (isset($reportData['provider_stats'])) {
                $this->info('Provider Usage:');
                $providerTable = [];
                foreach ($reportData['provider_stats'] as $provider => $stats) {
                    $providerTable[] = [
                        ucwords($provider),
                        number_format($stats['usage_count']),
                        '₱' . number_format($stats['estimated_cost'], 2),
                    ];
                }
                $this->table(['Provider', 'Messages', 'Estimated Cost'], $providerTable);
            }
        }
    }

    /**
     * Output report as JSON.
     */
    protected function outputJson(array $reportData): void
    {
        $this->line(json_encode($reportData, JSON_PRETTY_PRINT));
    }

    /**
     * Output report as CSV.
     */
    protected function outputCsv(array $reportData, ?string $outputFile = null): void
    {
        if (empty($reportData)) {
            return;
        }

        $csvData = [];
        
        // Add summary data
        if (isset($reportData['summary'])) {
            $csvData[] = ['Section', 'Metric', 'Value'];
            foreach ($reportData['summary'] as $key => $value) {
                $csvData[] = ['Summary', ucwords(str_replace('_', ' ', $key)), $this->formatValue($key, $value)];
            }
        }

        // Convert to CSV string
        $csvContent = '';
        foreach ($csvData as $row) {
            $csvContent .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        if ($outputFile) {
            Storage::put($outputFile, $csvContent);
            $this->info("CSV report saved to: {$outputFile}");
        } else {
            $this->line($csvContent);
        }
    }

    /**
     * Output report to file.
     */
    protected function outputFile(array $reportData, ?string $outputFile = null): void
    {
        $outputFile = $outputFile ?? 'sms-report-' . now()->format('Y-m-d-H-i-s') . '.json';
        
        Storage::put($outputFile, json_encode($reportData, JSON_PRETTY_PRINT));
        $this->info("Report saved to: {$outputFile}");
    }

    /**
     * Send report via email.
     */
    protected function sendReportEmail(array $reportData, string $email): void
    {
        // This would integrate with your email system
        $this->info("Email functionality not implemented yet. Report would be sent to: {$email}");
    }

    /**
     * Format value for display.
     */
    protected function formatValue(string $key, $value): string
    {
        if (str_contains($key, 'cost') || str_contains($key, 'Cost')) {
            return '₱' . number_format((float) $value, 2);
        }
        
        if (str_contains($key, 'rate') || str_contains($key, 'Rate')) {
            return number_format((float) $value, 1) . '%';
        }
        
        if (is_numeric($value)) {
            return number_format((float) $value);
        }
        
        return (string) $value;
    }
}
