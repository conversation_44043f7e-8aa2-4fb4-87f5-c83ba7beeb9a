<?php

namespace App\Services;

use App\Models\Student;
use App\Models\Attendance;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class StudentExportService
{
    /**
     * Export students list.
     */
    public function exportStudents(array $filters = []): string
    {
        $query = Student::with(['attendance']);

        // Apply filters
        if (!empty($filters['grade_level'])) {
            $query->byGradeLevel($filters['grade_level']);
        }

        if (!empty($filters['section'])) {
            $query->bySection($filters['section']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('first_name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('last_name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('student_id', 'like', '%' . $filters['search'] . '%');
            });
        }

        $students = $query->get();

        $filename = 'students-export-' . now()->format('Y-m-d-H-i-s') . '.xlsx';
        $path = storage_path('app/public/exports/' . $filename);

        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        Excel::store(new StudentsExport($students), 'public/exports/' . $filename);

        return 'exports/' . $filename;
    }

    /**
     * Export attendance data for students.
     */
    public function exportAttendance(array $filters = []): string
    {
        $query = Attendance::with(['student', 'teacher', 'subject']);

        // Apply filters
        if (!empty($filters['student_id'])) {
            $query->where('student_id', $filters['student_id']);
        }

        if (!empty($filters['grade_level']) || !empty($filters['section'])) {
            $query->whereHas('student', function ($q) use ($filters) {
                if (!empty($filters['grade_level'])) {
                    $q->byGradeLevel($filters['grade_level']);
                }
                if (!empty($filters['section'])) {
                    $q->bySection($filters['section']);
                }
            });
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $attendance = $query->orderBy('date', 'desc')->get();

        $filename = 'attendance-export-' . now()->format('Y-m-d-H-i-s') . '.xlsx';
        $path = storage_path('app/public/exports/' . $filename);

        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        Excel::store(new AttendanceExport($attendance), 'public/exports/' . $filename);

        return 'exports/' . $filename;
    }

    /**
     * Export student analytics/summary.
     */
    public function exportAnalytics(array $filters = []): string
    {
        $students = Student::with(['attendance' => function ($query) use ($filters) {
            if (!empty($filters['date_from'])) {
                $query->where('date', '>=', $filters['date_from']);
            }
            if (!empty($filters['date_to'])) {
                $query->where('date', '<=', $filters['date_to']);
            }
        }])->get();

        $analytics = $students->map(function ($student) use ($filters) {
            $days = !empty($filters['days']) ? $filters['days'] : 30;
            
            return [
                'student_id' => $student->student_id,
                'name' => $student->full_name,
                'grade_level' => $student->grade_level,
                'section' => $student->section,
                'attendance_rate' => $student->getAttendanceRate($days),
                'total_present' => $student->attendance()->where('status', 'present')->count(),
                'total_absent' => $student->attendance()->where('status', 'absent')->count(),
                'total_late' => $student->attendance()->where('status', 'late')->count(),
                'is_at_risk' => $student->isAtRisk() ? 'Yes' : 'No',
                'parent_phone' => $student->parent_phone,
                'status' => $student->status->label(),
            ];
        });

        $filename = 'student-analytics-' . now()->format('Y-m-d-H-i-s') . '.xlsx';
        $path = storage_path('app/public/exports/' . $filename);

        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        Excel::store(new StudentAnalyticsExport($analytics), 'public/exports/' . $filename);

        return 'exports/' . $filename;
    }
}

class StudentsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected Collection $students;

    public function __construct(Collection $students)
    {
        $this->students = $students;
    }

    public function collection()
    {
        return $this->students;
    }

    public function headings(): array
    {
        return [
            'Student ID',
            'Full Name',
            'Grade Level',
            'Section',
            'Parent Phone',
            'Emergency Contact',
            'Status',
            'Attendance Rate (%)',
            'Created At',
        ];
    }

    public function map($student): array
    {
        return [
            $student->student_id,
            $student->full_name,
            $student->grade_level,
            $student->section,
            $student->parent_phone,
            $student->emergency_contact,
            $student->status->label(),
            $student->getAttendanceRate(),
            $student->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class AttendanceExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected Collection $attendance;

    public function __construct(Collection $attendance)
    {
        $this->attendance = $attendance;
    }

    public function collection()
    {
        return $this->attendance;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Student ID',
            'Student Name',
            'Grade Level',
            'Section',
            'Time In',
            'Time Out',
            'Status',
            'Remarks',
            'Teacher',
        ];
    }

    public function map($attendance): array
    {
        return [
            $attendance->date->format('Y-m-d'),
            $attendance->student->student_id,
            $attendance->student->full_name,
            $attendance->student->grade_level,
            $attendance->student->section,
            $attendance->formatted_time_in,
            $attendance->formatted_time_out,
            $attendance->status->label(),
            $attendance->remarks,
            $attendance->teacher->full_name ?? 'N/A',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class StudentAnalyticsExport implements FromCollection, WithHeadings, WithStyles, ShouldAutoSize
{
    protected Collection $analytics;

    public function __construct(Collection $analytics)
    {
        $this->analytics = $analytics;
    }

    public function collection()
    {
        return $this->analytics;
    }

    public function headings(): array
    {
        return [
            'Student ID',
            'Name',
            'Grade Level',
            'Section',
            'Attendance Rate (%)',
            'Total Present',
            'Total Absent',
            'Total Late',
            'At Risk',
            'Parent Phone',
            'Status',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
