<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Enums\StudentStatus;
use App\Services\QRCodeService;
use App\Services\StudentImportService;
use App\Services\StudentExportService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class StudentController extends Controller
{
    protected QRCodeService $qrCodeService;
    protected StudentImportService $importService;
    protected StudentExportService $exportService;

    public function __construct(
        QRCodeService $qrCodeService,
        StudentImportService $importService,
        StudentExportService $exportService
    ) {
        $this->qrCodeService = $qrCodeService;
        $this->importService = $importService;
        $this->exportService = $exportService;
    }

    /**
     * Display a paginated list of students with search and filter options.
     */
    public function index(Request $request): View|JsonResponse|RedirectResponse
    {
        try {
            $query = Student::with(['attendance']);

            // Search functionality
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%")
                      ->orWhere('student_id', 'like', "%{$search}%")
                      ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$search}%"]);
                });
            }

            // Filter by grade level
            if ($request->filled('grade_level')) {
                $query->byGradeLevel($request->get('grade_level'));
            }

            // Filter by section
            if ($request->filled('section')) {
                $query->bySection($request->get('section'));
            }

            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $students = $query->paginate($perPage);

            // Add attendance statistics to each student
            $students->getCollection()->transform(function ($student) {
                $student->attendance_rate = $student->getAttendanceRate();
                $student->is_at_risk = $student->isAtRisk();
                return $student;
            });

            // API response
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => $students,
                    'filters' => [
                        'grade_levels' => ['11', '12'],
                        'sections' => Student::distinct()->pluck('section')->sort()->values(),
                        'statuses' => StudentStatus::cases(),
                    ],
                ]);
            }

            // Web response
            return view('students.index', compact('students'));
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to retrieve students',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to retrieve students: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(): View|JsonResponse|RedirectResponse
    {
        try {
            $validationRules = Student::validationRules();
            $sections = Student::distinct()->pluck('section')->sort()->values();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'validation_rules' => $validationRules,
                        'grade_levels' => ['11', '12'],
                        'sections' => $sections,
                        'statuses' => StudentStatus::cases(),
                    ],
                ]);
            }

            return view('students.create', compact('validationRules', 'sections'));
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to load create form',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to load create form: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created student with QR code generation.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        try {
            // Validation
            $validator = Validator::make($request->all(), Student::validationRules());

            if ($validator->fails()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $validator->errors(),
                    ], 422);
                }

                return back()->withErrors($validator)->withInput();
            }

            DB::beginTransaction();

            // Handle photo upload
            $photoPath = null;
            if ($request->hasFile('photo')) {
                $photoPath = $request->file('photo')->store('student-photos', 'public');
            }

            // Create student
            $student = Student::create([
                'student_id' => $request->student_id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'middle_name' => $request->middle_name,
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'parent_phone' => $request->parent_phone,
                'emergency_contact' => $request->emergency_contact,
                'photo_path' => $photoPath,
                'status' => $request->status ?? StudentStatus::ACTIVE->value,
            ]);

            // Generate QR code
            $qrCodePath = $this->qrCodeService->generateForStudent($student);
            $student->update(['qr_code_path' => $qrCodePath]);

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student created successfully',
                    'data' => $student->load('attendance'),
                ], 201);
            }

            return redirect()->route('students.show', $student)
                           ->with('success', 'Student created successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create student',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to create student: ' . $e->getMessage())
                         ->withInput();
        }
    }

    /**
     * Display the specified student with attendance history and analytics.
     */
    public function show(Request $request, Student $student): View|JsonResponse|RedirectResponse
    {
        try {
            // Load relationships
            $student->load(['attendance' => function ($query) {
                $query->orderBy('date', 'desc')->limit(50);
            }, 'teachers']);

            // Get attendance analytics
            $attendanceStats = [
                'total_days' => $student->attendance()->count(),
                'present_days' => $student->attendance()->where('status', 'present')->count(),
                'absent_days' => $student->attendance()->where('status', 'absent')->count(),
                'late_days' => $student->attendance()->where('status', 'late')->count(),
                'attendance_rate' => $student->getAttendanceRate(),
                'is_at_risk' => $student->isAtRisk(),
            ];

            // Recent attendance (last 30 days)
            $recentAttendance = $student->attendance()
                                      ->where('date', '>=', now()->subDays(30))
                                      ->orderBy('date', 'desc')
                                      ->get();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'student' => $student,
                        'attendance_stats' => $attendanceStats,
                        'recent_attendance' => $recentAttendance,
                    ],
                ]);
            }

            return view('students.show', compact('student', 'attendanceStats', 'recentAttendance'));
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to retrieve student',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to retrieve student: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(Student $student): View|JsonResponse|RedirectResponse
    {
        try {
            $validationRules = Student::validationRules();
            $sections = Student::distinct()->pluck('section')->sort()->values();

            // Modify validation rules for update (exclude current student from unique check)
            $validationRules['student_id'] = ['required', 'string', 'max:50', 'unique:students,student_id,' . $student->id];

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'student' => $student,
                        'validation_rules' => $validationRules,
                        'grade_levels' => ['11', '12'],
                        'sections' => $sections,
                        'statuses' => StudentStatus::cases(),
                    ],
                ]);
            }

            return view('students.edit', compact('student', 'validationRules', 'sections'));
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to load edit form',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to load edit form: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified student with validation and QR code regeneration if needed.
     */
    public function update(Request $request, Student $student): RedirectResponse|JsonResponse
    {
        try {
            // Validation rules for update
            $validationRules = Student::validationRules();
            $validationRules['student_id'] = ['required', 'string', 'max:50', 'unique:students,student_id,' . $student->id];

            $validator = Validator::make($request->all(), $validationRules);

            if ($validator->fails()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $validator->errors(),
                    ], 422);
                }

                return back()->withErrors($validator)->withInput();
            }

            DB::beginTransaction();

            // Check if critical data changed (requires QR code regeneration)
            $criticalFields = ['student_id', 'first_name', 'last_name', 'grade_level', 'section'];
            $needsQRRegeneration = false;

            foreach ($criticalFields as $field) {
                if ($student->$field !== $request->$field) {
                    $needsQRRegeneration = true;
                    break;
                }
            }

            // Handle photo upload
            $photoPath = $student->photo_path;
            if ($request->hasFile('photo')) {
                // Delete old photo
                if ($photoPath && Storage::disk('public')->exists($photoPath)) {
                    Storage::disk('public')->delete($photoPath);
                }
                $photoPath = $request->file('photo')->store('student-photos', 'public');
            }

            // Update student data
            $student->update([
                'student_id' => $request->student_id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'middle_name' => $request->middle_name,
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'parent_phone' => $request->parent_phone,
                'emergency_contact' => $request->emergency_contact,
                'photo_path' => $photoPath,
                'status' => $request->status ?? $student->status,
            ]);

            // Regenerate QR code if needed
            if ($needsQRRegeneration) {
                $qrCodePath = $this->qrCodeService->regenerateForStudent($student);
                $student->update(['qr_code_path' => $qrCodePath]);
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student updated successfully',
                    'data' => $student->fresh()->load('attendance'),
                    'qr_regenerated' => $needsQRRegeneration,
                ]);
            }

            return redirect()->route('students.show', $student)
                           ->with('success', 'Student updated successfully' .
                                  ($needsQRRegeneration ? ' (QR code regenerated)' : ''));
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update student',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to update student: ' . $e->getMessage())
                         ->withInput();
        }
    }

    /**
     * Soft delete the specified student with confirmation.
     */
    public function destroy(Request $request, Student $student): RedirectResponse|JsonResponse
    {
        try {
            // Check if confirmation is provided
            if (!$request->has('confirmed') || !$request->boolean('confirmed')) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Deletion requires confirmation',
                        'requires_confirmation' => true,
                        'student' => [
                            'id' => $student->id,
                            'name' => $student->full_name,
                            'student_id' => $student->student_id,
                        ],
                    ], 400);
                }

                return back()->with('error', 'Deletion requires confirmation');
            }

            DB::beginTransaction();

            // Soft delete the student (update status to inactive)
            $student->update(['status' => StudentStatus::INACTIVE]);

            // Optionally, you can implement actual soft deletes using Laravel's SoftDeletes trait
            // For now, we're just marking as inactive

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student deleted successfully',
                ]);
            }

            return redirect()->route('students.index')
                           ->with('success', 'Student deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete student',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to delete student: ' . $e->getMessage());
        }
    }

    /**
     * Bulk import students from CSV/Excel file.
     */
    public function bulkImport(Request $request): RedirectResponse|JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => ['required', 'file', 'mimes:csv,xlsx,xls', 'max:10240'], // 10MB max
            ]);

            if ($validator->fails()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $validator->errors(),
                    ], 422);
                }

                return back()->withErrors($validator);
            }

            // Validate file structure first
            $fileValidation = $this->importService->validateFile($request->file('file'));

            if (!$fileValidation['valid']) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File validation failed',
                        'error' => $fileValidation['error'],
                    ], 422);
                }

                return back()->with('error', 'File validation failed: ' . $fileValidation['error']);
            }

            // Import students
            $results = $this->importService->import($request->file('file'));

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Import completed. {$results['successful']} students imported successfully, {$results['failed']} failed.",
                    'data' => $results,
                ]);
            }

            $message = "Import completed. {$results['successful']} students imported successfully";
            if ($results['failed'] > 0) {
                $message .= ", {$results['failed']} failed.";
            }

            return back()->with('success', $message)->with('import_results', $results);
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Import failed',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate QR codes for multiple students (batch generation).
     */
    public function generateQRCodes(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_ids' => ['required', 'array', 'min:1'],
                'student_ids.*' => ['required', 'integer', 'exists:students,id'],
                'grade_level' => ['nullable', 'in:11,12'],
                'section' => ['nullable', 'string', 'max:50'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Generate QR codes based on provided student IDs or filters
            if ($request->has('student_ids')) {
                $results = $this->qrCodeService->generateBatch($request->student_ids);
            } else {
                $results = $this->qrCodeService->generateForGradeSection(
                    $request->grade_level,
                    $request->section
                );
            }

            $successful = collect($results)->where('status', 'success')->count();
            $failed = collect($results)->where('status', 'error')->count();

            return response()->json([
                'success' => true,
                'message' => "QR code generation completed. {$successful} successful, {$failed} failed.",
                'data' => $results,
                'summary' => [
                    'total' => count($results),
                    'successful' => $successful,
                    'failed' => $failed,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'QR code generation failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export student attendance data.
     */
    public function exportAttendance(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => ['nullable', 'integer', 'exists:students,id'],
                'grade_level' => ['nullable', 'in:11,12'],
                'section' => ['nullable', 'string', 'max:50'],
                'date_from' => ['nullable', 'date'],
                'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
                'status' => ['nullable', 'in:present,absent,late,excused'],
                'format' => ['nullable', 'in:xlsx,csv'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $filters = $request->only([
                'student_id', 'grade_level', 'section',
                'date_from', 'date_to', 'status'
            ]);

            $filePath = $this->exportService->exportAttendance($filters);

            return response()->json([
                'success' => true,
                'message' => 'Attendance data exported successfully',
                'data' => [
                    'file_path' => $filePath,
                    'download_url' => Storage::url($filePath),
                    'filters_applied' => $filters,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Export failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get student attendance analytics and statistics.
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => ['nullable', 'integer', 'exists:students,id'],
                'grade_level' => ['nullable', 'in:11,12'],
                'section' => ['nullable', 'string', 'max:50'],
                'date_from' => ['nullable', 'date'],
                'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
                'days' => ['nullable', 'integer', 'min:1', 'max:365'],
                'export' => ['nullable', 'boolean'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $days = $request->get('days', 30);
            $query = Student::with(['attendance']);

            // Apply filters
            if ($request->filled('student_id')) {
                $query->where('id', $request->student_id);
            }

            if ($request->filled('grade_level')) {
                $query->byGradeLevel($request->grade_level);
            }

            if ($request->filled('section')) {
                $query->bySection($request->section);
            }

            $students = $query->get();

            // Calculate analytics for each student
            $analytics = $students->map(function ($student) use ($days, $request) {
                $attendanceQuery = $student->attendance();

                if ($request->filled('date_from')) {
                    $attendanceQuery->where('date', '>=', $request->date_from);
                }

                if ($request->filled('date_to')) {
                    $attendanceQuery->where('date', '<=', $request->date_to);
                }

                $totalAttendance = $attendanceQuery->count();
                $presentCount = $attendanceQuery->where('status', 'present')->count();
                $absentCount = $attendanceQuery->where('status', 'absent')->count();
                $lateCount = $attendanceQuery->where('status', 'late')->count();

                return [
                    'student_id' => $student->student_id,
                    'name' => $student->full_name,
                    'grade_level' => $student->grade_level,
                    'section' => $student->section,
                    'attendance_rate' => $student->getAttendanceRate($days),
                    'total_days' => $totalAttendance,
                    'present_days' => $presentCount,
                    'absent_days' => $absentCount,
                    'late_days' => $lateCount,
                    'is_at_risk' => $student->isAtRisk(),
                    'status' => $student->status->label(),
                ];
            });

            // Overall statistics
            $overallStats = [
                'total_students' => $students->count(),
                'active_students' => $students->where('status', StudentStatus::ACTIVE)->count(),
                'at_risk_students' => $analytics->where('is_at_risk', true)->count(),
                'average_attendance_rate' => $analytics->avg('attendance_rate'),
                'total_present' => $analytics->sum('present_days'),
                'total_absent' => $analytics->sum('absent_days'),
                'total_late' => $analytics->sum('late_days'),
            ];

            // Export if requested
            if ($request->boolean('export')) {
                $filters = $request->only(['student_id', 'grade_level', 'section', 'date_from', 'date_to', 'days']);
                $filePath = $this->exportService->exportAnalytics($filters);

                return response()->json([
                    'success' => true,
                    'message' => 'Analytics exported successfully',
                    'data' => [
                        'analytics' => $analytics,
                        'overall_stats' => $overallStats,
                        'export_file' => $filePath,
                        'download_url' => Storage::url($filePath),
                    ],
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'analytics' => $analytics,
                    'overall_stats' => $overallStats,
                    'filters_applied' => $request->only(['student_id', 'grade_level', 'section', 'date_from', 'date_to', 'days']),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
