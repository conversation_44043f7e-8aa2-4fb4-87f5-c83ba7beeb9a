<?php

namespace Database\Factories;

use App\Enums\SmsType;
use App\Models\SmsTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SmsTemplate>
 */
class SmsTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = SmsTemplate::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $templates = [
            [
                'name' => 'attendance_absent',
                'content' => 'ALERT: Your child {{student_name}} is marked ABSENT for {{subject}} on {{date}} at {{time}}. Please contact the school if this is incorrect. - {{school_name}}',
                'type' => SmsType::ATTENDANCE,
                'description' => 'Sent when a student is marked absent',
            ],
            [
                'name' => 'attendance_late',
                'content' => 'NOTICE: Your child {{student_name}} arrived LATE for {{subject}} on {{date}} at {{time}}. Arrival time: {{arrival_time}}. - {{school_name}}',
                'type' => SmsType::ATTENDANCE,
                'description' => 'Sent when a student arrives late',
            ],
            [
                'name' => 'attendance_present',
                'content' => 'Good news! Your child {{student_name}} is PRESENT for {{subject}} on {{date}} at {{time}}. - {{school_name}}',
                'type' => SmsType::ATTENDANCE,
                'description' => 'Sent when a student is marked present (optional)',
            ],
            [
                'name' => 'emergency_alert',
                'content' => 'EMERGENCY ALERT: {{message}} Please contact the school immediately at {{contact_info}} for more information. - {{school_name}}',
                'type' => SmsType::EMERGENCY,
                'description' => 'Emergency notifications to parents',
            ],
            [
                'name' => 'general_announcement',
                'content' => 'ANNOUNCEMENT: {{message}} For more details, please visit our website or contact the school office. - {{school_name}}',
                'type' => SmsType::ANNOUNCEMENT,
                'description' => 'General school announcements',
            ],
        ];

        $template = $this->faker->randomElement($templates);

        return [
            'name' => $template['name'],
            'content' => $template['content'],
            'type' => $template['type'],
            'description' => $template['description'],
            'variables' => $this->extractVariables($template['content']),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'character_count' => strlen($template['content']),
            'estimated_parts' => $this->calculateParts($template['content']),
        ];
    }

    /**
     * Indicate that the template is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create an attendance template.
     */
    public function attendance(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'attendance_' . $this->faker->randomElement(['absent', 'late', 'present']),
            'type' => SmsType::ATTENDANCE,
            'content' => 'Your child {{student_name}} is {{status}} for {{subject}} on {{date}}. - {{school_name}}',
            'variables' => ['student_name', 'status', 'subject', 'date', 'school_name'],
        ]);
    }

    /**
     * Create an emergency template.
     */
    public function emergency(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'emergency_' . $this->faker->word(),
            'type' => SmsType::EMERGENCY,
            'content' => 'EMERGENCY: {{message}} Contact: {{contact_info}} - {{school_name}}',
            'variables' => ['message', 'contact_info', 'school_name'],
        ]);
    }

    /**
     * Create an announcement template.
     */
    public function announcement(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'announcement_' . $this->faker->word(),
            'type' => SmsType::ANNOUNCEMENT,
            'content' => 'ANNOUNCEMENT: {{message}} - {{school_name}}',
            'variables' => ['message', 'school_name'],
        ]);
    }

    /**
     * Create a bulk template.
     */
    public function bulk(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'bulk_' . $this->faker->word(),
            'type' => SmsType::BULK,
            'content' => 'Dear {{parent_name}}, {{message}} Best regards, {{school_name}}',
            'variables' => ['parent_name', 'message', 'school_name'],
        ]);
    }

    /**
     * Create a long template (multi-part SMS).
     */
    public function long(): static
    {
        $longContent = 'Dear {{parent_name}}, this is a very long message that will definitely exceed the standard SMS character limit of 160 characters. ' .
                      'It contains important information about {{student_name}} and their academic progress in {{subject}}. ' .
                      'Please contact us at {{contact_info}} if you have any questions. Thank you for your attention. - {{school_name}}';

        return $this->state(fn (array $attributes) => [
            'content' => $longContent,
            'character_count' => strlen($longContent),
            'estimated_parts' => $this->calculateParts($longContent),
            'variables' => ['parent_name', 'student_name', 'subject', 'contact_info', 'school_name'],
        ]);
    }

    /**
     * Extract variables from template content.
     */
    private function extractVariables(string $content): array
    {
        preg_match_all('/\{\{(\w+)\}\}/', $content, $matches);
        return array_unique($matches[1]);
    }

    /**
     * Calculate estimated SMS parts.
     */
    private function calculateParts(string $content): int
    {
        $length = strlen($content);
        
        if ($length <= 160) {
            return 1;
        }
        
        // For multi-part SMS, each part can contain 153 characters
        return (int) ceil($length / 153);
    }
}
