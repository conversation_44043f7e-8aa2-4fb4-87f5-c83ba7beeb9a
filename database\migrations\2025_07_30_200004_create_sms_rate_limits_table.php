<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_rate_limits', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Rate limit identification
            $table->string('identifier')->comment('Rate limit identifier (teacher_id, ip_address, etc.)');
            $table->string('type')->comment('teacher, ip, global, provider');
            
            // Rate limit configuration
            $table->integer('limit_count')->comment('Number of messages allowed');
            $table->string('limit_period')->comment('Time period (minute, hour, day, month)');
            $table->timestamp('period_start')->comment('Start of current period');
            $table->timestamp('period_end')->comment('End of current period');
            
            // Current usage
            $table->integer('current_count')->default(0)->comment('Current usage count');
            $table->timestamp('last_request_at')->nullable()->comment('Last request timestamp');
            
            // Rate limit status
            $table->boolean('is_exceeded')->default(false)->comment('Rate limit exceeded');
            $table->timestamp('reset_at')->nullable()->comment('When rate limit resets');
            $table->integer('exceeded_count')->default(0)->comment('Number of times exceeded');
            
            // Timestamps
            $table->timestamps();
            
            // Unique constraint
            $table->unique(['identifier', 'type', 'limit_period'], 'unique_rate_limit');
            
            // Indexes
            $table->index(['identifier', 'type']); // Rate limit lookups
            $table->index(['is_exceeded', 'reset_at']); // Exceeded rate limits
            $table->index(['period_end']); // Period cleanup
            $table->index(['last_request_at']); // Activity tracking
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_rate_limits');
    }
};
