<?php

namespace App\Listeners;

use App\Enums\AttendanceStatus;
use App\Events\AttendanceRecorded;
use App\Services\TextBeeSmsService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendAttendanceSmsNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The SMS service instance.
     */
    protected TextBeeSmsService $smsService;

    /**
     * Create the event listener.
     */
    public function __construct(TextBeeSmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Handle the event.
     */
    public function handle(AttendanceRecorded $event): void
    {
        $attendance = $event->attendance;
        $student = $attendance->student;
        $subject = $attendance->subject;

        // Only send SMS for certain statuses and if parent phone exists
        if (!$this->shouldSendSms($attendance) || !$student->parent_phone) {
            return;
        }

        try {
            // Use the new TextBee SMS service with attendance alert method
            $result = $this->smsService->sendAttendanceAlert($attendance);

            if ($result['success']) {
                Log::info('Attendance SMS sent successfully', [
                    'student_id' => $student->id,
                    'attendance_id' => $attendance->id,
                    'status' => $attendance->status->value,
                    'phone' => $student->parent_phone,
                    'message_id' => $result['message_id'],
                    'cost' => $result['cost'] ?? null,
                ]);
            } else {
                Log::warning('Failed to send attendance SMS', [
                    'student_id' => $student->id,
                    'attendance_id' => $attendance->id,
                    'error' => $result['error'],
                    'phone' => $student->parent_phone,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception sending attendance SMS', [
                'student_id' => $student->id,
                'attendance_id' => $attendance->id,
                'error' => $e->getMessage(),
                'phone' => $student->parent_phone,
            ]);

            // Optionally retry or handle the failure
            $this->fail($e);
        }
    }

    /**
     * Determine if SMS should be sent for this attendance record.
     */
    protected function shouldSendSms(Attendance $attendance): bool
    {
        // Send SMS for absent, late, or when marking present after being absent
        return in_array($attendance->status, [
            AttendanceStatus::ABSENT,
            AttendanceStatus::LATE,
            AttendanceStatus::PRESENT, // When student arrives
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(AttendanceRecorded $event, \Throwable $exception): void
    {
        Log::error('Attendance SMS notification job failed', [
            'attendance_id' => $event->attendance->id,
            'student_id' => $event->attendance->student_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
