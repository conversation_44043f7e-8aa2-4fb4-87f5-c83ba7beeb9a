@extends('layouts.app')

@section('title', 'Dashboard - Analytics & Reporting')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportDashboard('overview', 'pdf')">Overview (PDF)</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportDashboard('overview', 'excel')">Overview (Excel)</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportDashboard('attendance_stats', 'csv')">Attendance Stats (CSV)</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportDashboard('student_risk', 'excel')">Student Risk (Excel)</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            @if(isset($error))
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> {{ $error }}
                </div>
            @endif

            <!-- Key Metrics Row -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Students
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-students">
                                        {{ $dashboardData['key_metrics']['total_students'] ?? 0 }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Today's Attendance Rate
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="attendance-rate">
                                        {{ $dashboardData['key_metrics']['attendance_rate'] ?? 0 }}%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Present Today
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="present-today">
                                        {{ $dashboardData['key_metrics']['today_present'] ?? 0 }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Absent Today
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="absent-today">
                                        {{ $dashboardData['key_metrics']['today_absent'] ?? 0 }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <!-- Weekly Trends Chart -->
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Weekly Attendance Trends</h6>
                            <div class="dropdown no-arrow">
                                <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right shadow">
                                    <a class="dropdown-item" href="#" onclick="loadTrendAnalysis('weekly')">Weekly View</a>
                                    <a class="dropdown-item" href="#" onclick="loadTrendAnalysis('monthly')">Monthly View</a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-area">
                                <canvas id="weeklyTrendsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Grade Level Distribution -->
                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Grade Level Distribution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-pie pt-4 pb-2">
                                <canvas id="gradeDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Tables Row -->
            <div class="row">
                <!-- At-Risk Students -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">At-Risk Students</h6>
                            <a href="#" class="btn btn-sm btn-primary" onclick="loadStudentRisk()">
                                View All
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="at-risk-table">
                                    <thead>
                                        <tr>
                                            <th>Student</th>
                                            <th>Grade</th>
                                            <th>Attendance Rate</th>
                                            <th>Risk Level</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(isset($dashboardData['at_risk_students']))
                                            @foreach($dashboardData['at_risk_students'] as $student)
                                                <tr>
                                                    <td>{{ $student['name'] }}</td>
                                                    <td>{{ $student['grade_level'] }}-{{ $student['section'] }}</td>
                                                    <td>{{ $student['attendance_rate'] }}%</td>
                                                    <td>
                                                        <span class="badge badge-{{ $student['risk_level'] === 'high' ? 'danger' : ($student['risk_level'] === 'medium' ? 'warning' : 'success') }}">
                                                            {{ ucfirst($student['risk_level']) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush" id="recent-activities">
                                @if(isset($dashboardData['recent_activities']))
                                    @foreach($dashboardData['recent_activities'] as $activity)
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ $activity['student_name'] }}</h6>
                                                <small>{{ $activity['time'] }}</small>
                                            </div>
                                            <p class="mb-1">
                                                <span class="badge badge-{{ $activity['status'] === 'present' ? 'success' : ($activity['status'] === 'absent' ? 'danger' : 'warning') }}">
                                                    {{ ucfirst($activity['status']) }}
                                                </span>
                                                in {{ $activity['subject_name'] }}
                                            </p>
                                            <small>Teacher: {{ $activity['teacher_name'] }}</small>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-primary btn-block" onclick="generateSF2Report()">
                                        <i class="fas fa-file-pdf"></i> Generate SF2 Report
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-primary btn-block" onclick="generateSF4Report()">
                                        <i class="fas fa-file-excel"></i> Generate SF4 Report
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-info btn-block" onclick="loadTeacherReports()">
                                        <i class="fas fa-chalkboard-teacher"></i> Teacher Reports
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-success btn-block" onclick="loadParentNotifications()">
                                        <i class="fas fa-sms"></i> SMS Notifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Processing...</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dashboard JavaScript functionality
let weeklyTrendsChart, gradeDistributionChart;

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    
    // Auto-refresh every 5 minutes
    setInterval(refreshDashboard, 300000);
});

function initializeCharts() {
    // Weekly Trends Chart
    const weeklyCtx = document.getElementById('weeklyTrendsChart').getContext('2d');
    const weeklyData = @json($dashboardData['weekly_trends'] ?? []);
    
    weeklyTrendsChart = new Chart(weeklyCtx, {
        type: 'line',
        data: {
            labels: weeklyData.map(d => d.day),
            datasets: [{
                label: 'Attendance Rate',
                data: weeklyData.map(d => d.attendance_rate),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Grade Distribution Chart
    const gradeCtx = document.getElementById('gradeDistributionChart').getContext('2d');
    const gradeData = @json($dashboardData['chart_data']['grade_distribution'] ?? []);
    
    gradeDistributionChart = new Chart(gradeCtx, {
        type: 'doughnut',
        data: {
            labels: gradeData.map(d => d.grade_level),
            datasets: [{
                data: gradeData.map(d => d.count),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}
</script>
@endpush
