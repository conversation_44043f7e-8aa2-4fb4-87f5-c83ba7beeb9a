<?php

namespace App\Http\Controllers;

use App\Enums\AttendanceStatus;
use App\Enums\StudentStatus;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Subject;
use App\Services\AttendanceReportService;
use App\Services\AttendanceExportService;
use App\Services\DashboardAnalyticsService;
use App\Services\StudentRiskAssessmentService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class DashboardController extends Controller
{
    protected AttendanceReportService $reportService;
    protected AttendanceExportService $exportService;
    protected DashboardAnalyticsService $analyticsService;
    protected StudentRiskAssessmentService $riskService;

    public function __construct(
        AttendanceReportService $reportService,
        AttendanceExportService $exportService,
        DashboardAnalyticsService $analyticsService,
        StudentRiskAssessmentService $riskService
    ) {
        $this->reportService = $reportService;
        $this->exportService = $exportService;
        $this->analyticsService = $analyticsService;
        $this->riskService = $riskService;
    }

    /**
     * Main dashboard with key metrics and charts.
     */
    public function index(Request $request): View|JsonResponse
    {
        try {
            $cacheKey = 'dashboard_data_' . Auth::id() . '_' . now()->format('Y-m-d-H');
            
            $dashboardData = Cache::remember($cacheKey, 3600, function () use ($request) {
                return $this->buildDashboardData($request);
            });

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => $dashboardData,
                ]);
            }

            return view('dashboard.index', compact('dashboardData'));
        } catch (\Exception $e) {
            Log::error('Dashboard error: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString(),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to load dashboard data',
                ], 500);
            }

            return view('dashboard.index', [
                'dashboardData' => $this->getEmptyDashboardData(),
                'error' => 'Failed to load dashboard data',
            ]);
        }
    }

    /**
     * Overall attendance statistics.
     */
    public function getAttendanceStats(Request $request): JsonResponse
    {
        try {
            $dateFrom = $request->input('date_from', now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->input('date_to', now()->format('Y-m-d'));
            $gradeLevel = $request->input('grade_level');
            $section = $request->input('section');
            $subjectId = $request->input('subject_id');

            $cacheKey = "attendance_stats_{$dateFrom}_{$dateTo}_{$gradeLevel}_{$section}_{$subjectId}";
            
            $stats = Cache::remember($cacheKey, 1800, function () use ($dateFrom, $dateTo, $gradeLevel, $section, $subjectId) {
                return $this->analyticsService->getAttendanceStatistics($dateFrom, $dateTo, [
                    'grade_level' => $gradeLevel,
                    'section' => $section,
                    'subject_id' => $subjectId,
                ]);
            });

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('Attendance stats error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve attendance statistics',
            ], 500);
        }
    }

    /**
     * AI-powered student risk assessment.
     */
    public function getStudentRisk(Request $request): JsonResponse
    {
        try {
            $gradeLevel = $request->input('grade_level');
            $section = $request->input('section');
            $riskLevel = $request->input('risk_level', 'all'); // all, high, medium, low
            $limit = $request->input('limit', 50);

            $cacheKey = "student_risk_{$gradeLevel}_{$section}_{$riskLevel}_" . now()->format('Y-m-d');
            
            $riskAssessment = Cache::remember($cacheKey, 7200, function () use ($gradeLevel, $section, $riskLevel, $limit) {
                return $this->riskService->assessStudentRisk([
                    'grade_level' => $gradeLevel,
                    'section' => $section,
                    'risk_level' => $riskLevel,
                    'limit' => $limit,
                ]);
            });

            return response()->json([
                'success' => true,
                'data' => $riskAssessment,
            ]);
        } catch (\Exception $e) {
            Log::error('Student risk assessment error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to assess student risk',
            ], 500);
        }
    }

    /**
     * Attendance pattern analysis.
     */
    public function getAttendanceTraits(Request $request): JsonResponse
    {
        try {
            $studentId = $request->input('student_id');
            $gradeLevel = $request->input('grade_level');
            $section = $request->input('section');
            $days = $request->input('days', 30);

            $cacheKey = "attendance_traits_{$studentId}_{$gradeLevel}_{$section}_{$days}_" . now()->format('Y-m-d');
            
            $traits = Cache::remember($cacheKey, 3600, function () use ($studentId, $gradeLevel, $section, $days) {
                return $this->analyticsService->analyzeAttendancePatterns([
                    'student_id' => $studentId,
                    'grade_level' => $gradeLevel,
                    'section' => $section,
                    'days' => $days,
                ]);
            });

            return response()->json([
                'success' => true,
                'data' => $traits,
            ]);
        } catch (\Exception $e) {
            Log::error('Attendance traits error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to analyze attendance patterns',
            ], 500);
        }
    }

    /**
     * Weekly/monthly attendance trends.
     */
    public function getTrendAnalysis(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', 'monthly'); // weekly, monthly, quarterly
            $gradeLevel = $request->input('grade_level');
            $section = $request->input('section');
            $subjectId = $request->input('subject_id');

            $cacheKey = "trend_analysis_{$period}_{$gradeLevel}_{$section}_{$subjectId}_" . now()->format('Y-m-d');
            
            $trends = Cache::remember($cacheKey, 3600, function () use ($period, $gradeLevel, $section, $subjectId) {
                return $this->analyticsService->getTrendAnalysis($period, [
                    'grade_level' => $gradeLevel,
                    'section' => $section,
                    'subject_id' => $subjectId,
                ]);
            });

            return response()->json([
                'success' => true,
                'data' => $trends,
            ]);
        } catch (\Exception $e) {
            Log::error('Trend analysis error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to analyze attendance trends',
            ], 500);
        }
    }

    /**
     * Automated SF2 form generation.
     */
    public function generateSF2(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'grade_level' => ['required', 'string'],
                'section' => ['required', 'string'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'date_from' => ['required', 'date'],
                'date_to' => ['required', 'date', 'after_or_equal:date_from'],
                'format' => ['in:pdf,excel,csv'],
            ]);

            $parameters = [
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'subject_id' => $request->subject_id,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
            ];

            $format = $request->input('format', 'pdf');
            
            $report = $this->reportService->generateReport($parameters);

            return response()->json([
                'success' => true,
                'message' => 'SF2 form generated successfully',
                'data' => [
                    'report_path' => $report['path'],
                    'download_url' => Storage::url($report['path']),
                    'format' => $report['format'],
                    'generated_at' => $report['generated_at'],
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('SF2 generation error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate SF2 form: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Automated SF4 form generation.
     */
    public function generateSF4(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'grade_level' => ['required', 'string'],
                'section' => ['required', 'string'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'date_from' => ['required', 'date'],
                'date_to' => ['required', 'date', 'after_or_equal:date_from'],
                'format' => ['in:pdf,excel,csv'],
            ]);

            $parameters = [
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'subject_id' => $request->subject_id,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
            ];

            $format = $request->input('format', 'pdf');
            
            $report = $this->reportService->generateReport($parameters);

            return response()->json([
                'success' => true,
                'message' => 'SF4 form generated successfully',
                'data' => [
                    'report_path' => $report['path'],
                    'download_url' => Storage::url($report['path']),
                    'format' => $report['format'],
                    'generated_at' => $report['generated_at'],
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('SF4 generation error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate SF4 form: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Teacher-specific attendance reports.
     */
    public function getTeacherReports(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->input('teacher_id', Auth::user()->teacher?->id);
            $dateFrom = $request->input('date_from', now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->input('date_to', now()->format('Y-m-d'));
            $subjectId = $request->input('subject_id');

            if (!$teacherId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Teacher ID is required',
                ], 400);
            }

            $cacheKey = "teacher_reports_{$teacherId}_{$dateFrom}_{$dateTo}_{$subjectId}";

            $reports = Cache::remember($cacheKey, 1800, function () use ($teacherId, $dateFrom, $dateTo, $subjectId) {
                return $this->analyticsService->getTeacherReports($teacherId, $dateFrom, $dateTo, [
                    'subject_id' => $subjectId,
                ]);
            });

            return response()->json([
                'success' => true,
                'data' => $reports,
            ]);
        } catch (\Exception $e) {
            Log::error('Teacher reports error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve teacher reports',
            ], 500);
        }
    }

    /**
     * SMS notification history.
     */
    public function getParentNotifications(Request $request): JsonResponse
    {
        try {
            $studentId = $request->input('student_id');
            $dateFrom = $request->input('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->input('date_to', now()->format('Y-m-d'));
            $status = $request->input('status'); // sent, failed, pending
            $perPage = $request->input('per_page', 20);

            $cacheKey = "parent_notifications_{$studentId}_{$dateFrom}_{$dateTo}_{$status}_" . now()->format('Y-m-d-H');

            $notifications = Cache::remember($cacheKey, 1800, function () use ($studentId, $dateFrom, $dateTo, $status, $perPage) {
                return $this->analyticsService->getParentNotifications([
                    'student_id' => $studentId,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'status' => $status,
                    'per_page' => $perPage,
                ]);
            });

            return response()->json([
                'success' => true,
                'data' => $notifications,
            ]);
        } catch (\Exception $e) {
            Log::error('Parent notifications error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve parent notifications',
            ], 500);
        }
    }

    /**
     * Export dashboard data.
     */
    public function exportDashboard(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'export_type' => ['required', 'in:overview,attendance_stats,student_risk,trends,teacher_reports'],
                'format' => ['required', 'in:pdf,excel,csv'],
                'date_from' => ['nullable', 'date'],
                'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
                'grade_level' => ['nullable', 'string'],
                'section' => ['nullable', 'string'],
                'subject_id' => ['nullable', 'integer', 'exists:subjects,id'],
                'teacher_id' => ['nullable', 'integer', 'exists:teachers,id'],
            ]);

            $exportType = $request->export_type;
            $format = $request->format;
            $parameters = $request->only([
                'date_from', 'date_to', 'grade_level', 'section',
                'subject_id', 'teacher_id'
            ]);

            // Set default date range if not provided
            if (!$parameters['date_from']) {
                $parameters['date_from'] = now()->startOfMonth()->format('Y-m-d');
            }
            if (!$parameters['date_to']) {
                $parameters['date_to'] = now()->format('Y-m-d');
            }

            $exportData = $this->prepareDashboardExportData($exportType, $parameters);
            $filePath = $this->exportService->exportDashboardData($exportType, $format, $exportData, $parameters);

            return response()->json([
                'success' => true,
                'message' => 'Dashboard data exported successfully',
                'data' => [
                    'export_type' => $exportType,
                    'format' => $format,
                    'file_path' => $filePath,
                    'download_url' => Storage::url($filePath),
                    'generated_at' => now(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Dashboard export error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to export dashboard data: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Build comprehensive dashboard data.
     */
    protected function buildDashboardData(Request $request): array
    {
        $today = now();
        $startOfMonth = $today->copy()->startOfMonth();

        // Key metrics
        $keyMetrics = $this->getKeyMetrics($today);

        // Today's attendance summary
        $todayAttendance = $this->getTodayAttendanceSummary($today);

        // Weekly trends
        $weeklyTrends = $this->getWeeklyTrends($today);

        // At-risk students
        $atRiskStudents = $this->getAtRiskStudents();

        // Recent activities
        $recentActivities = $this->getRecentActivities();

        // Chart data
        $chartData = $this->getChartData($startOfMonth, $today);

        return [
            'key_metrics' => $keyMetrics,
            'today_attendance' => $todayAttendance,
            'weekly_trends' => $weeklyTrends,
            'at_risk_students' => $atRiskStudents,
            'recent_activities' => $recentActivities,
            'chart_data' => $chartData,
            'last_updated' => $today->toISOString(),
        ];
    }

    /**
     * Get key dashboard metrics.
     */
    protected function getKeyMetrics(Carbon $date): array
    {
        $totalStudents = Student::active()->count();
        $todayAttendance = Attendance::where('date', $date->format('Y-m-d'))->count();
        $todayPresent = Attendance::where('date', $date->format('Y-m-d'))
            ->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE, AttendanceStatus::EXCUSED])
            ->count();
        $todayAbsent = Attendance::where('date', $date->format('Y-m-d'))
            ->where('status', AttendanceStatus::ABSENT)
            ->count();

        $attendanceRate = $todayAttendance > 0 ? round(($todayPresent / $todayAttendance) * 100, 1) : 0;

        return [
            'total_students' => $totalStudents,
            'today_attendance_count' => $todayAttendance,
            'today_present' => $todayPresent,
            'today_absent' => $todayAbsent,
            'attendance_rate' => $attendanceRate,
            'active_teachers' => Teacher::active()->count(),
            'total_subjects' => Subject::where('is_active', true)->count(),
        ];
    }

    /**
     * Get today's attendance summary by grade level.
     */
    protected function getTodayAttendanceSummary(Carbon $date): array
    {
        $summary = DB::table('attendance')
            ->join('students', 'attendance.student_id', '=', 'students.id')
            ->where('attendance.date', $date->format('Y-m-d'))
            ->select(
                'students.grade_level',
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN attendance.status IN ("present", "late", "excused") THEN 1 ELSE 0 END) as present'),
                DB::raw('SUM(CASE WHEN attendance.status = "absent" THEN 1 ELSE 0 END) as absent')
            )
            ->groupBy('students.grade_level')
            ->orderBy('students.grade_level')
            ->get()
            ->map(function ($item) {
                $item->attendance_rate = $item->total > 0 ? round(($item->present / $item->total) * 100, 1) : 0;
                return $item;
            });

        return $summary->toArray();
    }

    /**
     * Get weekly attendance trends.
     */
    protected function getWeeklyTrends(Carbon $date): array
    {
        $startOfWeek = $date->copy()->startOfWeek();
        $trends = [];

        for ($i = 0; $i < 7; $i++) {
            $currentDate = $startOfWeek->copy()->addDays($i);

            $dayAttendance = Attendance::where('date', $currentDate->format('Y-m-d'))
                ->selectRaw('
                    COUNT(*) as total,
                    SUM(CASE WHEN status IN ("present", "late", "excused") THEN 1 ELSE 0 END) as present,
                    SUM(CASE WHEN status = "absent" THEN 1 ELSE 0 END) as absent
                ')
                ->first();

            $trends[] = [
                'date' => $currentDate->format('Y-m-d'),
                'day' => $currentDate->format('l'),
                'total' => $dayAttendance->total ?? 0,
                'present' => $dayAttendance->present ?? 0,
                'absent' => $dayAttendance->absent ?? 0,
                'attendance_rate' => $dayAttendance->total > 0
                    ? round(($dayAttendance->present / $dayAttendance->total) * 100, 1)
                    : 0,
            ];
        }

        return $trends;
    }

    /**
     * Get at-risk students.
     */
    protected function getAtRiskStudents(int $limit = 10): array
    {
        return Student::active()
            ->with(['attendance' => function ($query) {
                $query->where('date', '>=', now()->subDays(30))
                      ->orderBy('date', 'desc');
            }])
            ->get()
            ->filter(function ($student) {
                return $student->isAtRisk();
            })
            ->take($limit)
            ->map(function ($student) {
                return [
                    'id' => $student->id,
                    'student_id' => $student->student_id,
                    'name' => $student->full_name,
                    'grade_level' => $student->grade_level,
                    'section' => $student->section,
                    'attendance_rate' => $student->getAttendanceRate(),
                    'recent_absences' => $student->attendance()
                        ->where('date', '>=', now()->subDays(7))
                        ->where('status', AttendanceStatus::ABSENT)
                        ->count(),
                    'risk_level' => $this->calculateRiskLevel($student),
                ];
            })
            ->sortByDesc('recent_absences')
            ->values()
            ->toArray();
    }

    /**
     * Get recent activities.
     */
    protected function getRecentActivities(int $limit = 10): array
    {
        return Attendance::with(['student', 'teacher', 'subject'])
            ->where('created_at', '>=', now()->subHours(24))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($attendance) {
                return [
                    'id' => $attendance->id,
                    'type' => 'attendance_recorded',
                    'student_name' => $attendance->student->full_name ?? 'Unknown',
                    'teacher_name' => $attendance->teacher->full_name ?? 'Unknown',
                    'subject_name' => $attendance->subject->name ?? 'Unknown',
                    'status' => $attendance->status->value,
                    'time' => $attendance->created_at->diffForHumans(),
                    'timestamp' => $attendance->created_at->toISOString(),
                ];
            })
            ->toArray();
    }

    /**
     * Get chart data for dashboard visualizations.
     */
    protected function getChartData(Carbon $startDate, Carbon $endDate): array
    {
        // Daily attendance chart data
        $dailyData = $this->getDailyAttendanceChartData($startDate, $endDate);

        // Grade level distribution
        $gradeDistribution = $this->getGradeLevelDistribution();

        // Status distribution
        $statusDistribution = $this->getStatusDistribution($startDate, $endDate);

        // Monthly trends
        $monthlyTrends = $this->getMonthlyTrendsData();

        return [
            'daily_attendance' => $dailyData,
            'grade_distribution' => $gradeDistribution,
            'status_distribution' => $statusDistribution,
            'monthly_trends' => $monthlyTrends,
        ];
    }

    /**
     * Get daily attendance chart data.
     */
    protected function getDailyAttendanceChartData(Carbon $startDate, Carbon $endDate): array
    {
        $data = [];
        $current = $startDate->copy();

        while ($current->lte($endDate)) {
            $dayData = Attendance::where('date', $current->format('Y-m-d'))
                ->selectRaw('
                    COUNT(*) as total,
                    SUM(CASE WHEN status = "present" THEN 1 ELSE 0 END) as present,
                    SUM(CASE WHEN status = "absent" THEN 1 ELSE 0 END) as absent,
                    SUM(CASE WHEN status = "late" THEN 1 ELSE 0 END) as late,
                    SUM(CASE WHEN status = "excused" THEN 1 ELSE 0 END) as excused
                ')
                ->first();

            $data[] = [
                'date' => $current->format('Y-m-d'),
                'day' => $current->format('D'),
                'total' => $dayData->total ?? 0,
                'present' => $dayData->present ?? 0,
                'absent' => $dayData->absent ?? 0,
                'late' => $dayData->late ?? 0,
                'excused' => $dayData->excused ?? 0,
            ];

            $current->addDay();
        }

        return $data;
    }

    /**
     * Get grade level distribution.
     */
    protected function getGradeLevelDistribution(): array
    {
        return Student::active()
            ->selectRaw('grade_level, COUNT(*) as count')
            ->groupBy('grade_level')
            ->orderBy('grade_level')
            ->get()
            ->map(function ($item) {
                return [
                    'grade_level' => $item->grade_level,
                    'count' => $item->count,
                ];
            })
            ->toArray();
    }

    /**
     * Get status distribution for the period.
     */
    protected function getStatusDistribution(Carbon $startDate, Carbon $endDate): array
    {
        return Attendance::whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->map(function ($item) {
                return [
                    'status' => $item->status,
                    'label' => AttendanceStatus::from($item->status)->label(),
                    'count' => $item->count,
                    'color' => AttendanceStatus::from($item->status)->color(),
                ];
            })
            ->toArray();
    }

    /**
     * Get monthly trends data.
     */
    protected function getMonthlyTrendsData(): array
    {
        $months = [];
        $current = now()->subMonths(5)->startOfMonth();

        for ($i = 0; $i < 6; $i++) {
            $monthData = Attendance::whereBetween('date', [
                $current->format('Y-m-01'),
                $current->format('Y-m-t')
            ])
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status IN ("present", "late", "excused") THEN 1 ELSE 0 END) as present,
                SUM(CASE WHEN status = "absent" THEN 1 ELSE 0 END) as absent
            ')
            ->first();

            $months[] = [
                'month' => $current->format('M Y'),
                'total' => $monthData->total ?? 0,
                'present' => $monthData->present ?? 0,
                'absent' => $monthData->absent ?? 0,
                'attendance_rate' => $monthData->total > 0
                    ? round(($monthData->present / $monthData->total) * 100, 1)
                    : 0,
            ];

            $current->addMonth();
        }

        return $months;
    }

    /**
     * Calculate risk level for a student.
     */
    protected function calculateRiskLevel(Student $student): string
    {
        $attendanceRate = $student->getAttendanceRate();
        $recentAbsences = $student->attendance()
            ->where('date', '>=', now()->subDays(7))
            ->where('status', AttendanceStatus::ABSENT)
            ->count();

        if ($attendanceRate < 60 || $recentAbsences >= 4) {
            return 'high';
        } elseif ($attendanceRate < 75 || $recentAbsences >= 2) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Prepare data for dashboard export.
     */
    protected function prepareDashboardExportData(string $exportType, array $parameters): array
    {
        return match($exportType) {
            'overview' => $this->prepareOverviewExportData($parameters),
            'attendance_stats' => $this->prepareAttendanceStatsExportData($parameters),
            'student_risk' => $this->prepareStudentRiskExportData($parameters),
            'trends' => $this->prepareTrendsExportData($parameters),
            'teacher_reports' => $this->prepareTeacherReportsExportData($parameters),
            default => [],
        };
    }

    /**
     * Prepare overview export data.
     */
    protected function prepareOverviewExportData(array $parameters): array
    {
        $startDate = Carbon::parse($parameters['date_from']);
        $endDate = Carbon::parse($parameters['date_to']);

        return [
            'period' => [
                'from' => $startDate->format('Y-m-d'),
                'to' => $endDate->format('Y-m-d'),
            ],
            'summary' => $this->getKeyMetrics(now()),
            'daily_breakdown' => $this->getDailyAttendanceChartData($startDate, $endDate),
            'grade_distribution' => $this->getGradeLevelDistribution(),
            'at_risk_students' => $this->getAtRiskStudents(20),
        ];
    }

    /**
     * Prepare attendance stats export data.
     */
    protected function prepareAttendanceStatsExportData(array $parameters): array
    {
        return $this->analyticsService->getAttendanceStatistics(
            $parameters['date_from'],
            $parameters['date_to'],
            $parameters
        );
    }

    /**
     * Prepare student risk export data.
     */
    protected function prepareStudentRiskExportData(array $parameters): array
    {
        return $this->riskService->assessStudentRisk($parameters);
    }

    /**
     * Prepare trends export data.
     */
    protected function prepareTrendsExportData(array $parameters): array
    {
        return $this->analyticsService->getTrendAnalysis('monthly', $parameters);
    }

    /**
     * Prepare teacher reports export data.
     */
    protected function prepareTeacherReportsExportData(array $parameters): array
    {
        $teacherId = $parameters['teacher_id'] ?? Auth::user()->teacher?->id;

        if (!$teacherId) {
            return [];
        }

        return $this->analyticsService->getTeacherReports(
            $teacherId,
            $parameters['date_from'],
            $parameters['date_to'],
            $parameters
        );
    }

    /**
     * Get empty dashboard data for error states.
     */
    protected function getEmptyDashboardData(): array
    {
        return [
            'key_metrics' => [
                'total_students' => 0,
                'today_attendance_count' => 0,
                'today_present' => 0,
                'today_absent' => 0,
                'attendance_rate' => 0,
                'active_teachers' => 0,
                'total_subjects' => 0,
            ],
            'today_attendance' => [],
            'weekly_trends' => [],
            'at_risk_students' => [],
            'recent_activities' => [],
            'chart_data' => [
                'daily_attendance' => [],
                'grade_distribution' => [],
                'status_distribution' => [],
                'monthly_trends' => [],
            ],
            'last_updated' => now()->toISOString(),
        ];
    }
}
